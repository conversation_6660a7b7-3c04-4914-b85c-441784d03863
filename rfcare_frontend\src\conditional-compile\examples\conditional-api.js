/**
 * 条件编译 API 示例
 * 展示如何在 JavaScript 文件中使用条件编译
 * 
 * <AUTHOR>
 */

import { getCurrentCompanyConfig, isCompany } from '../config'

// 基础 API 配置
const baseApiConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
}

// #ifdef COMPANY_BOE
// BOE 公司特有的 API 配置
const boeApiConfig = {
  ...baseApiConfig,
  baseURL: '/api/boe',
  headers: {
    ...baseApiConfig.headers,
    'X-Company': 'BOE',
    'X-Custom-Header': 'boe-special'
  },
  // BOE 特有的拦截器配置
  interceptors: {
    request: (config) => {
      console.log('BOE API 请求拦截器')
      config.headers['X-BOE-Token'] = localStorage.getItem('boe-token')
      return config
    },
    response: (response) => {
      console.log('BOE API 响应拦截器')
      return response
    }
  }
}

// BOE 特有的 API 方法
export const boeSpecialApi = {
  // 获取 BOE 特有数据
  getBoeData() {
    console.log('调用 BOE 特有 API')
    return fetch('/api/boe/special-data')
  },
  
  // BOE 高级报表 API
  getAdvancedReports(params) {
    console.log('获取 BOE 高级报表', params)
    return fetch('/api/boe/advanced-reports', {
      method: 'POST',
      body: JSON.stringify(params)
    })
  }
}
// #endif

// #ifdef COMPANY_DEFAULT
// 默认公司的 API 配置
const defaultApiConfig = {
  ...baseApiConfig,
  baseURL: '/api',
  headers: {
    ...baseApiConfig.headers,
    'X-Company': 'DEFAULT'
  }
}

// 默认版本的 API 方法
export const defaultApi = {
  // 获取默认数据
  getDefaultData() {
    console.log('调用默认 API')
    return fetch('/api/default-data')
  },
  
  // 基础报表 API
  getBasicReports(params) {
    console.log('获取基础报表', params)
    return fetch('/api/basic-reports', {
      method: 'POST',
      body: JSON.stringify(params)
    })
  }
}
// #endif

// 通用 API 方法（所有版本都有）
export const commonApi = {
  // 用户登录
  login(credentials) {
    const config = getCurrentCompanyConfig()
    console.log(`${config.name} - 用户登录`)
    
    // #ifdef COMPANY_BOE
    // BOE 特有的登录逻辑
    return fetch('/api/boe/login', {
      method: 'POST',
      body: JSON.stringify({
        ...credentials,
        companyCode: 'BOE'
      })
    })
    // #endif
    
    // #ifdef COMPANY_DEFAULT
    // 默认版本的登录逻辑
    return fetch('/api/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    })
    // #endif
  },
  
  // 获取用户信息
  getUserInfo() {
    const config = getCurrentCompanyConfig()
    console.log(`${config.name} - 获取用户信息`)
    
    return fetch(config.api.baseUrl + '/user/info')
  },
  
  // 根据公司返回不同的配置
  getApiConfig() {
    // #ifdef COMPANY_BOE
    return boeApiConfig
    // #endif
    
    // #ifdef COMPANY_DEFAULT
    return defaultApiConfig
    // #endif
    
    // 默认返回基础配置
    return baseApiConfig
  }
}

// 条件编译的工具函数
export const conditionalApiUtils = {
  // 根据公司执行不同的 API 调用
  callCompanySpecificApi(apiName, ...args) {
    // #ifdef COMPANY_BOE
    if (boeSpecialApi[apiName]) {
      return boeSpecialApi[apiName](...args)
    }
    // #endif
    
    // #ifdef COMPANY_DEFAULT
    if (defaultApi[apiName]) {
      return defaultApi[apiName](...args)
    }
    // #endif
    
    console.warn(`API ${apiName} 在当前公司版本中不可用`)
    return Promise.reject(new Error(`API ${apiName} not available`))
  },
  
  // 检查 API 是否可用
  isApiAvailable(apiName) {
    // #ifdef COMPANY_BOE
    return boeSpecialApi.hasOwnProperty(apiName)
    // #endif
    
    // #ifdef COMPANY_DEFAULT
    return defaultApi.hasOwnProperty(apiName)
    // #endif
    
    return false
  }
}

// 导出配置
export const apiConfig = commonApi.getApiConfig()

// 默认导出
export default {
  commonApi,
  conditionalApiUtils,
  apiConfig,
  // #ifdef COMPANY_BOE
  boeSpecialApi,
  // #endif
  // #ifdef COMPANY_DEFAULT
  defaultApi
  // #endif
}
