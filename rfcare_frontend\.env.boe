# BOE 公司环境配置
NODE_ENV = production

# 页面标题
VUE_APP_TITLE = BOE 服务管理系统

# 公司标识
VUE_APP_COMPANY = boe

# 接口请求地址，会设置到 axios 的 baseURL 参数上
VUE_APP_API_ROOT = /api/
VUE_APP_WS_API_ROOT = wss://boe.rfcare.cn

# 是否开启 CDN 支持，开启设置 ON，关闭设置 OFF
VUE_APP_CDN = OFF

# 是否开启 gzip 压缩，开启设置 ON，关闭设置 OFF
VUE_APP_GZIP = ON

# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VUE_APP_DEBUG_TOOL =

# BOE 特定配置
VUE_APP_THEME_COLOR = #1890ff
VUE_APP_LOGO_URL = /images/logo-boe.png
VUE_APP_ENABLE_ADVANCED_REPORTS = true
VUE_APP_ENABLE_CUSTOM_DASHBOARD = true
VUE_APP_ENABLE_MULTI_TENANT = false
