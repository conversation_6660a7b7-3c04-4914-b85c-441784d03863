<template>
  <div class="conditional-component">
    <h2>条件编译示例组件</h2>
    
    <!-- 使用条件编译注释的方式 -->
    <!-- #ifdef COMPANY_BOE -->
    <div class="boe-section">
      <h3>BOE 专属功能</h3>
      <p>这个内容只在 BOE 公司版本中显示</p>
      <el-button type="primary" @click="boeSpecialFunction">BOE 特殊功能</el-button>
    </div>
    <!-- #endif -->
    
    <!-- #ifdef COMPANY_DEFAULT -->
    <div class="default-section">
      <h3>默认版本功能</h3>
      <p>这个内容只在默认版本中显示</p>
      <el-button type="success" @click="defaultFunction">默认功能</el-button>
    </div>
    <!-- #endif -->
    
    <!-- 使用 v-if 的运行时条件编译 -->
    <div v-if="$isBOE()" class="runtime-boe">
      <h3>运行时 BOE 功能</h3>
      <p>使用运行时判断的 BOE 功能</p>
    </div>
    
    <div v-if="$isDefault()" class="runtime-default">
      <h3>运行时默认功能</h3>
      <p>使用运行时判断的默认功能</p>
    </div>
    
    <!-- 根据公司显示不同的样式 -->
    <div :class="getCompanyClass()">
      <h3>公司主题样式</h3>
      <p>当前公司：{{ currentCompany.name }}</p>
      <p>主题色：{{ currentCompany.theme.primaryColor }}</p>
    </div>
    
    <!-- 功能开关示例 -->
    <div v-if="$isFeatureEnabled('enableAdvancedReports')" class="advanced-reports">
      <h3>高级报表功能</h3>
      <p>这个功能只在启用高级报表的公司版本中显示</p>
    </div>
    
    <div v-if="$isFeatureEnabled('enableCustomDashboard')" class="custom-dashboard">
      <h3>自定义仪表板</h3>
      <p>这个功能只在启用自定义仪表板的公司版本中显示</p>
    </div>
  </div>
</template>

<script>
import { conditionalMixin } from '../utils'

export default {
  name: 'ConditionalComponent',
  mixins: [conditionalMixin],
  methods: {
    // #ifdef COMPANY_BOE
    boeSpecialFunction() {
      this.$message.success('BOE 特殊功能执行成功！')
      console.log('BOE 特殊功能被调用')
    },
    // #endif
    
    // #ifdef COMPANY_DEFAULT
    defaultFunction() {
      this.$message.success('默认功能执行成功！')
      console.log('默认功能被调用')
    },
    // #endif
    
    getCompanyClass() {
      return this.$byCompany({
        boe: 'boe-theme',
        default: 'default-theme'
      }, 'default-theme')
    }
  }
}
</script>

<style scoped>
.conditional-component {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}
.conditional-component h2 {
    color: #333;
    margin-bottom: 20px;
}
.conditional-component h3 {
    color: #666;
    margin: 15px 0 10px 0;
}
.boe-section,
.default-section,
.runtime-boe,
.runtime-default {
    border: 1px solid #ddd;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}
.boe-section {
    background-color: #e6f7ff;
    border-color: #1890ff;
}
.default-section {
    background-color: #f6ffed;
    border-color: #52c41a;
}
.runtime-boe {
    background-color: #fff2e8;
    border-color: #fa8c16;
}
.runtime-default {
    background-color: #f9f0ff;
    border-color: #722ed1;
}

/* #ifdef COMPANY_BOE */
.boe-theme {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

/* #endif */

/* #ifdef COMPANY_DEFAULT */
.default-theme {
    background: linear-gradient(135deg, #409eff, #67c23a);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

/* #endif */
.advanced-reports,
.custom-dashboard {
    background-color: #fff7e6;
    border: 1px solid #ffd591;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}
</style>
