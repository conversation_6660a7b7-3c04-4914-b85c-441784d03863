/**
 * 微信服务号订阅消息
 * @param {Array} tmplIds 模板ID数组
 */
async function subscribeMessage(tmplIds) {
  uni.requestSubscribeMessage({
    tmplIds: tmplIds,
    success: (resMsg) => {
      console.log("提交订单消息推送", resMsg);
      if (this.sendMsgId && this.sendMsgId.length) {
        this.sendMsgId.forEach((item, index) => {
          if (resMsg[item] === "accept") {
            // 用户同意订阅该模板
            console.log("用户同意订阅该模板", item);
          }
          if (resMsg[item] === "reject") {
            // 用户拒绝订阅该模板
            console.log("用户拒绝订阅该模板", item);
          }
        });
      }
    },
    fail: (err) => {
      console.error("订阅消息失败", err);
    },
  });
}
