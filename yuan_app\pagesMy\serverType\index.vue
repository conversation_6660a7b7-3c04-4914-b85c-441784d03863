<template>
	<view class="my-server-type-info-page">
		<view class="top-header-line"></view>
		<view class='item-block'>
			<view class="server-content" style="white-space: pre-wrap;">
				<rich-text :nodes="serverType.serverDesc"></rich-text>
			</view>
		</view>
		<view v-if="devShow" class='device-wrap safe-area-inset-bottom'>
			<view class="title">
				选择要开通的设备
				<u-icon name="close" color="#bbb" size="26" style="position: absolute; right: 0rpx; top: 0rpx;" @click="devShow = false"></u-icon>
			</view>
			<scroll-view scroll-y class="scroll-view" style="max-height: 500rpx;">
				<view class="device-list">
					 <!-- :class="{ 'actived': devCheckId === dev.id }" -->
					<view v-for="(dev, index) in devs" :key="index" class="device-item" @click="handleDevBuy(dev)">
						{{ (dev.devName || dev.devCode) || '' }}({{ dev.devCode }})
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="fetchDevices">立即开通</u-button>
		</view>
	</view>
</template>

<script>
	import graceRichText  from '@/utils/richtext.js'
	import {subscribeMessage} from '@/utils/WXSubscribeMessage.js'
	export default {
		data() {
			return {
				serverType: {},
				devs: [],
				devShow: false,
				devCheckId: undefined,
				serverId: undefined,
			}
		},
		onLoad(option) {
			this.serverId = option.id;
			this.fetchServerInfo(option.id)
			uni.setNavigationBarTitle({
				title: option.name || '服务'
			});
		},
		methods: {
			handleDevBuy(dev) {
				this.devShow = false;
				// this.devCheckId = dev.id
				this.$navTo(`pagesFamily/device/spec/buy?devId=${dev.id}&serverId=${this.serverType.id}`);
			},
			fetchServerInfo(id) {
				subscribeMessage(["Kj9MF9UOFgoWP1jE8-cX-q59IfXsSKw09bQm1k9Ks00","OqtHV0UVra5VQWScF43ZyCdsDbRWGHFF37dw2465rps"]);
				this.$u.api.fetchMyServerPackageInfo({ serverId: id }).then(res => {
					let _content = res || {};
					// if (_content.serverDesc) {
					// 	_content.serverDesc = _content.serverDesc.replace(/\<img/gi, '<img style="max-width: 100%; height: auto" ')
					// }
					this.serverType = _content
					this.serverType.serverDesc = graceRichText.format(this.serverType.serverDesc);
				})
			},
			fetchDevices() {
				this.$u.api.fetchOpenServerDevList({ serverId: this.serverId }).then(res => {
					this.devs = res || []
					if (!this.devs || !this.devs.length) {
						this.$toast('您没有绑定设备或设备已拥有该服务，无需购买', 3000);
						return;
					} else {
						this.devShow = true;
					}
				})
			},
			handleShowDevs() {
				if (!this.devs || !this.devs.length) {
					this.$toast('您没有绑定设备或设备已拥有该服务，无需购买');
					return;
				}
				this.devShow = true;
				// let _filters = this.devs.filter(f => f.id === this.devCheckId);
				// if (_filters && _filters.length) {
				// 	let _dev = _filters[0];
				// 	// this.$navTo(`/pagesDevice/spec/index?devId=${_dev.id}&devName=${_dev.devName}&modelName=${_dev.productModelName}&serverStatus=${_dev.serverStatus}&serverStatusName=${_dev.serverStatusName}&serverEndDate=${_dev.serverEndDate}&activeStatus=${_dev.activeStatus}`)
				// 	this.$navTo(`pagesFamily/device/spec/buy?devId=${this.devCheckId}&serverId=${this.serverType.id}`);
				// }
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}	
</style>
<style lang="scss" scoped>
.my-server-type-info-page {
	padding: 20rpx;
	.item-block {
		background: white;
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		border-radius: 16rpx;
		.server-content {
			img {
				width: 50rpx;
			}
		}
	}
	.device-wrap {
		padding: 20rpx;
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		background: white;
		z-index: 1;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		.title {
			font-size: 30rpx;
			margin-top: 10rpx;
			color: #666;
			position: relative;
		}
		.device-list {
			display: block;
			margin-top: 20rpx;
			border-bottom: 2rpx solid #ececec;
			// padding-bottom: 20rpx;
			// margin-bottom: 26rpx;
			// max-height: 400rpx;
			// overflow-y: auto;
			.device-item {
				font-size: 24rpx;
				padding: 20rpx 0rpx;
				color: #666;
				&:nth-child(n + 2) {
					border-top: 2rpx solid #ececec;
				}
				&.actived {
					background: #01B09A;
					color: white;
					padding: 16rpx 10rpx;
					border-radius: 10rpx;
				}
			}
		}
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
