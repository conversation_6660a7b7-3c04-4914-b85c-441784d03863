<template>
	<view class="home-parlour-page" :style="{ 'overflow': danDisplayFlag ? 'hidden' : 'initial' }">
		<scroll-view scroll-y class="scroll-view" style="height: 100vh;">
			<view class="top-header-line"></view>

		<view class="r-flex top-wrap">
			<view class="r-flex-1">
				{{ (dev.devName || dev.devCode) || '' }}({{ dev.devCode }})
			</view>
			<view class="ta-r" style="width: 226rpx;">
				<!-- <view class="right-btn">···</view> -->
				<!--#ifdef H5 -->
				<image src="/static/images/common/icon-more.png"
					style="width: 85rpx; height: 85rpx; vertical-align: middle;"
					@click="$navTo(`pagesFamily/device/notActive-v2?devId=${dev.id}&devCode=${dev.devCode}`)"></image>
				<!--#endif -->
				<!--#ifndef H5 -->
				<DeanPopover ref="menuPopover"
					:btnList="[ { command: 'deviceDetails', name: '设备详情' }, { command: 'familySharing', name: '设备共享' } ]"
					modal-top-pos="11vw" modal-width="24vw" modal-opacity="1" direction="left" style="z-index: 2"
					@on-select="handleMenuClick" @on-show-change="handleShowChange">
					<image src="/static/images/common/icon-more.png"
						style="width: 85rpx; height: 85rpx; vertical-align: middle;"></image>
				</DeanPopover>
				<!--#endif -->
			</view>
		</view>
		<view v-if="danDisplayFlag"
			style="position: fixed; top: 0rpx; right: 0rpx; left: 0rpx; bottom: 0rpx; background: rgba(0, 0, 0, 0.3); z-index: 1;"
			@click="handleCloseDan"></view>

		<view v-if="tabActived === 1" class="top-bg"></view>

		<view v-if="getServerEndDate()" class="u-flex u-row-between">
			<view class="u-tips-color u-font-13">
				{{dev.serverEndDate ? `${dev.serverEndDate}&nbsp;&nbsp;到期` : ``}}
			</view>
			<view>
				<u-button shape="circle" size="mini"
					:custom-style="{ 'width': '120rpx', 'color': 'white', 'background-color': '#01B09A'}"
					hover-class="none" @click="$navTo(`pagesFamily/device/order/index?devId=${dev.id}`)">续费</u-button>
			</view>
		</view>

		<!--被监护人-->
		<older-avatar-view :olders="olders" mute />

		<view class="item-block">
			<u-tabs :list="tabs" :is-scroll="false" :current="tabActived" :item-width="100" bg-color="transparent"
				:active-item-style="{ 'color': '#01B09A' }"
				:bar-style="{ 'width': '20rpx', 'background-color': '#01B09A', 'border-radius': '20rpx;' }"
				@change="handleTabChange"></u-tabs>
			<view v-if="tabActived === 0">
				<view class="r-flex">
					<view class="r-flex-1">当前状态</view>
					<!-- <view class="r-flex-1" v-if="dev.personStatus === '0'" style="text-align: right;">
						<text class="icon iconfont icon-xuanzhong1" style="font-size: 32rpx; vertical-align: middle; margin-right: 10rpx; position: relative; top: -2rpx;"></text>
						无人
					</view>
					<view class="r-flex-1" v-if="dev.personStatus === '1'" style="text-align: right;">
						<text class="icon iconfont icon-xuanzhong1" style="font-size: 32rpx; color: #01B09A; vertical-align: middle; margin-right: 10rpx; position: relative; top: -2rpx;"></text>
						有人
					</view> -->
					<view v-if="dev.moveStatus === '0'" style="display: inline-block; margin-left: 20rpx;">
						<text class="icon iconfont icon-xuanzhong1"
							style="font-size: 32rpx; vertical-align: middle; margin-right: 10rpx; position: relative; top: -2rpx;"></text>{{ dev.moveStatusName || '' }}
					</view>
					<view v-if="dev.moveStatus === '1' || dev.moveStatus === '2'"
						style="display: inline-block; margin-left: 20rpx;">
						<text class="icon iconfont icon-xuanzhong1"
							style="font-size: 32rpx; color: #01B09A; vertical-align: middle; margin-right: 10rpx; position: relative; top: -2rpx;"></text>{{ dev.moveStatusName || '' }}
					</view>
				</view>
				<ParlourChart v-if="dev.id" ref="parlourChart" :num="dev.walkingDistance || 0" unit="米" desc="今日活动">
				</ParlourChart>
			</view>
			<EventComponent v-if="tabActived === 1 && dev.id" :devId="dev.id"></EventComponent>
		</view>
		<view v-if="tabActived === 0 && sleepValue && sleepValue.length" class="item-block">
			<view class="title">人员实时状态</view>
			<LtSleepCharts :sleepValue="sleepValue" :setStyle="setStyle"
				:colors="{ '监测到人': '#01B09A', '人员离开': '#F66C3E' }"></LtSleepCharts>
			<view class="dynamic-list">
				<view class="item">监测到人</view>
				<view class="item">人员离开</view>
			</view>
		</view>

		<view v-if="tabActived === 0" class="time-report">
			<view class="r-flex bottom-info">
				<view class="r-flex" style="flex: 1; justify-content: space-evenly;">
					<text class="icon iconfont icon-shijian1" style="width: 100rpx; font-size: 69rpx; color: #01B09A;"></text>
					<view class="r-flex-1">
						<view class="title">总停留时长</view>
						<view class="desc">{{ dev.parlorOtherStayDuration ? dev.parlorOtherStayDuration + '分' : '-' }}</view>
					</view>
				</view>
				<view class="r-flex" style="flex: 1;">
					<text class="icon iconfont icon-huodongshuju" style="width: 100rpx; font-size: 80rpx; color: #01B09A;"></text>
					<view class="r-flex-1">
						<view class="title">今日活动</view>
						<view class="desc">{{ dev.walkingDistance ? dev.walkingDistance + '米' : '-' }}</view>
					</view>
				</view>
			</view>
			<view class="r-flex" style="margin-top: 20rpx;">
				<view class="item-block vc-flex" @click="goReport" style="width: 100%; text-align: center;">
					活动报告 >>
				</view>
			</view>
		</view>

		<view v-if="tabActived === 0" class="item-block r-flex chart3d"
			@click="$navTo('pages/webview/index?id=' + dev.id)">
			<text class="icon iconfont icon-Dxuanzhuan" style="width: 110rpx; font-size: 70rpx; color: #01B09A;"></text>
			<text class="text">实时查看</text>
		</view>

	</scroll-view>
</view>
</template>

<script>
	import DeanPopover from '@/components/dean-popover/dean-popover.vue'
	import ParlourChart from './components/parlour-chart.vue';
	import EventComponent from '../components/event.vue';
	import LtSleepCharts from '../components/Lt-sleepCharts/Lt-sleepCharts.vue'
	export default {
		components: {
			ParlourChart,
			EventComponent,
			LtSleepCharts,
			DeanPopover
		},
		data() {
			return {
				tabs: [{
					name: '设备统计'
				}, {
					name: '事件列表'
				}],
				tabActived: 0,
				dev: {},
				currDevId: undefined,
				setStyle: {
					width: "100%"
				},
				sleepValue: [],
				devRealDataTimer: undefined,
				olders: [],
				danShow: false,
			}
		},
		computed: {
			danDisplayFlag() {
				return this.danShow
			}
		},
		watch: {
			'tabActived': function(val) {
				if (val === 0) {
					this.fetchDeviceInfo(this.currDevId);
					this.fetchPersonRealtimeStatusList(this.currDevId);
				}
			}
		},
		onLoad(option) {
			this.currDevId = option.id;
		},
		onShow() {
			this.init()
		},
		// onUnload() {
		// 	console.log("客厅Timer停止")
		// 	this.stopTimer()
		// },
		// onHide() {
		// 	console.log("客厅Timer停止")
		// 	this.stopTimer()
		// },
		methods: {
			onPullDownRefresh() {
				console.log("触发下拉刷新")
				setTimeout(() => {
					this.fetchDeviceInfo(this.currDevId);
					uni.stopPullDownRefresh()
				},1000)
			},
			getServerEndDate() {
			  if (this.dev.serverEndDate && (new Date(this.dev.serverEndDate)-30*24*3600*1000 <= new Date())) {
			    return true;
			  } else {
			    return false;
			  }
			},
			init(){
				this.fetchMyOlderListByDeviceId(this.currDevId);
				this.fetchDeviceInfo(this.currDevId);
				this.fetchPersonRealtimeStatusList(this.currDevId);
			},
			// stopTimer(){
			// 	if (this.devRealDataTimer) {
			// 		clearTimeout(this.devRealDataTimer);
			// 		this.devRealDataTimer = null;
			// 	}
			// },
			// 关闭黑色背景
			handleCloseDan() {
				this.danShow = false
				if (this.$refs['menuPopover']) {
					this.$refs['menuPopover'].handleClose();
				}
			},
			// 展示黑色背景
			handleShowChange(showFlag) {
				this.danShow = showFlag
			},
			// 点击跳转页面
			handleMenuClick(idx) {
				this.danShow = false;
				if (idx === 0) {
					this.$navTo(`pagesFamily/device/notActive-v2?devId=${this.dev.id}&devCode=${this.dev.devCode}`);
				} else if (idx === 1) {
					this.$navTo(
						`pagesFamily/devMember/index?id=${ this.dev.id }&name=${ this.dev.devName || this.dev.devCode }`
						);
				}
			},
			goReport() {
				uni.setStorageSync('temp_report_nav', {
					id: this.currDevId,
					devName: this.dev.devName,
					devCode: this.dev.devCode,
					devScene: this.dev.devScene,
					devSceneName: this.dev.devSceneName
				});
				this.$navTo(`pages/report/index`)
			},
			fetchDeviceInfo(id, isTimer) {
				this.$u.api.fetchDeviceInfo({
					devId: id
				}).then(res => {
					let _dev = res || {};
					this.dev = _dev;
					/* if (res && res.id && !isTimer) {
						this.devRealDataTimer = setInterval(() => {
							console.log("客厅页面Timer")
							this.fetchDeviceInfo(id, true)
						}, 5000)
					} */
				})
			},
			fetchPersonRealtimeStatusList(id) {
				this.$u.api.fetchPersonRealtimeStatusList({
					devId: id
				}).then(res => {
					let _times = [];
					(res || []).filter(f => f.startTime && f.endTime).map((m, i) => {
						let _startTime = Date.parse(new Date(m.startTime.replace(/-/g, '/'))) / 1000;
						let _endTime = Date.parse(new Date(m.endTime.replace(/-/g, '/'))) / 1000;
						_times.push({
							startTime: _startTime,
							endTime: _endTime,
							sleepState: m.status,
						})
						if (res[i + 1] && _endTime !== Date.parse(new Date(res[i + 1].startTime.replace(
								/-/g, '/'))) / 1000) {
							_times.push({
								startTime: _endTime,
								endTime: Date.parse(new Date(res[i + 1].startTime.replace(/-/g,
									'/'))) / 1000,
								sleepState: '人员离开',
							})
						}
					});
					this.sleepValue = _times;
				})
			},
			handleTabChange(index) {
				this.tabActived = index;
			},

			async fetchMyOlderListByDeviceId(devId) {
				const olders = await this.$u.api.fetchMyOlderListByDeviceId({
					devId
				}) || [];
				if (olders.length === 0) {
					this.olders = olders;
					return;
				}

				// for (let older of olders) {
				// 	older.isGuard = await this.$u.api.isGuardOlderWorryfree({
				// 		olderId: older.id
				// 	});
				// }

				this.olders = olders;
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;
	}
</style>

<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.home-parlour-page {
		padding: 20rpx;
	
	.top-wrap {
			position: relative;

			.right-btn {
				position: absolute;
				right: 0rpx;
				top: 0rpx;
				background: white;
				padding: 0rpx 20rpx;
				border-radius: 10rpx;
			}
		}
	
	.top-bg {
			position: fixed;
			top: 0rpx;
			left: 0rpx;
			right: 0rpx;
			height: 116rpx;
			background: white;
			z-index: -1;
		}

		.item-block {
			background: white;
			padding: 30rpx 20rpx;
			font-size: 28rpx;
			margin-top: 30rpx;
			border-radius: 20rpx;
		}

		.time-report {
			background: white;
			padding: 30rpx 20rpx;
			font-size: 28rpx;
			margin-top: 30rpx;
			border-radius: 20rpx;

			.bottom-info {
				margin-top: 20rpx;
				justify-content: space-around;
				padding-bottom: 20rpx;
				.title {
					font-size: 28rpx;
					font-weight: bold;
					color: #000;
				}
				.desc {
					font-size: 26rpx;
					margin-top: 10rpx;
					color: #888;
				}
				::v-deep .icon {
					text-align: center;
					margin-left: 50rpx;
				}
			}

			.item-block {
				height: 100%;
			}

			.icon-zuidatingliushichang {
				position: relative;
				top: 4rpx;
				width: 130rpx;
				font-size: 70rpx;
				color: #01B09A;
				text-align: center;
			}
		}

		.chart3d {
			justify-content: center;
			margin-top: 40rpx;

			.text {
				font-size: 36rpx;
			}
		}

		.dynamic-list {
			display: flex;
			flex-direction: row;
			justify-content: center;

			.item {
				width: 23%;
				border-radius: 10rpx;
				// background: #bbb;
				margin-top: 20rpx;
				text-align: center;
				color: #000;
				padding: 6rpx 0rpx;
				font-size: 22rpx;

				// padding-left: 20rpx;
				&:nth-child(1) {
					&::before {
						background-color: #01B09A;
					}
				}

				&:nth-child(2) {
					&::before {
						background-color: #F66C3E;
					}
				}

				&::before {
					position: relative;
					left: -10rpx;
					top: 2rpx;
					content: ' ';
					display: inline-block;
					width: 20rpx;
					height: 20rpx;
					border-radius: 4rpx;
				}
			}
		}
	}

	::v-deep .u-tabs-scorll-flex {
		justify-content: flex-start !important;
	}

	::v-deep .u-tab-item {
		flex: initial !important;
		width: 120rpx !important;
		margin-right: 20rpx !important;
		height: 62rpx !important;
		line-height: 44rpx !important;

	}

	::v-deep .u-tab-bar {
		left: 10rpx !important;
		bottom: -3rpx !important;
	}

	::v-deep .u-tabs {
		margin-bottom: 20rpx;
		border-bottom: 2rpx solid #ececec;
		padding-bottom: 14rpx;
	}
</style>
