<template>
  <div class="test-conditional">
    <h1>条件编译测试</h1>

    <div v-if="showBoeContent" class="boe-content">
      <h2>BOE 版本内容</h2>
      <p>这个内容只在 BOE 版本中显示</p>
    </div>

    <div v-if="showDefaultContent" class="default-content">
      <h2>默认版本内容</h2>
      <p>这个内容只在默认版本中显示</p>
    </div>

    <div class="common-content">
      <h2>通用内容</h2>
      <p>这个内容在所有版本中都显示</p>
      <p>当前公司: {{ currentCompany }}</p>
      <p>环境变量: {{ envCompany }}</p>
      <p>编译时变量: {{ compileTimeCompany }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestConditional',
  data() {
    return {
      currentCompany: 'unknown',
      envCompany: process.env.VUE_APP_COMPANY
    }
  },
  mounted() {
    // #ifdef COMPANY_BOE
    this.currentCompany = 'BOE'
    console.log('BOE 版本代码执行')
    // #endif

    // #ifdef COMPANY_DEFAULT
    this.currentCompany = 'DEFAULT'
    console.log('默认版本代码执行')
    // #endif
  }
}
</script>

<style scoped>
.test-conditional {
    padding: 20px;
}

/* #ifdef COMPANY_BOE */
.boe-content {
    background-color: #e6f7ff;
    border: 2px solid #1890ff;
    padding: 15px;
    margin: 10px 0;
}

/* #endif */

/* #ifdef COMPANY_DEFAULT */
.default-content {
    background-color: #f0f9ff;
    border: 2px solid #409eff;
    padding: 15px;
    margin: 10px 0;
}

/* #endif */
.common-content {
    background-color: #f5f5f5;
    border: 2px solid #ccc;
    padding: 15px;
    margin: 10px 0;
}
</style>
