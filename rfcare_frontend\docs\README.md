# 条件编译功能

## 简介

本项目实现了类似 uniapp 的条件编译功能，支持为不同公司进行私有化部署。

## 快速使用

### 构建命令

```bash
# BOE 版本
yarn start:boe    # 开发环境
yarn build:boe    # 生产构建

# 默认版本
yarn start        # 开发环境
yarn build        # 生产构建
```

### 基本语法

#### JavaScript 条件编译
```javascript
// #ifdef COMPANY_BOE
console.log('BOE 版本代码')
// #endif

// #ifdef COMPANY_DEFAULT
console.log('默认版本代码')
// #endif
```

#### Vue 模板条件编译
```vue
<!-- #ifdef COMPANY_BOE -->
<div>BOE 专属内容</div>
<!-- #endif -->

<!-- #ifdef COMPANY_DEFAULT -->
<div>默认版本内容</div>
<!-- #endif -->
```

#### CSS 条件编译
```css
/* #ifdef COMPANY_BOE */
.boe-style {
  color: #1890ff;
}
/* #endif */

/* #ifdef COMPANY_DEFAULT */
.default-style {
  color: #409EFF;
}
/* #endif */
```

### 运行时条件编译

```vue
<script>
import { conditionalMixin } from '@/conditional-compile'

export default {
  mixins: [conditionalMixin],
  methods: {
    handleClick() {
      if (this.$isBOE()) {
        console.log('BOE 版本逻辑')
      }
      
      if (this.$isDefault()) {
        console.log('默认版本逻辑')
      }
      
      // 根据公司返回不同值
      const config = this.$byCompany({
        boe: 'BOE 配置',
        default: '默认配置'
      })
    }
  }
}
</script>
```

## 文件结构

```
src/
├── conditional-compile/          # 条件编译模块
│   ├── config.js                # 公司配置
│   ├── utils.js                 # 工具函数
│   ├── index.js                 # 统一导出
│   └── examples/                # 示例代码
│       ├── ConditionalComponent.vue
│       ├── conditional-api.js
│       └── conditional-styles.scss
├── .env.boe                     # BOE 生产环境配置
├── .env.boe.development         # BOE 开发环境配置
└── docs/                        # 文档
    ├── 条件编译使用指南.md        # 详细使用指南
    └── README.md                # 快速开始
```

## 支持的公司

- **BOE**：京东方版本（`boe`）
- **DEFAULT**：默认版本（`default`）

## 详细文档

查看 [条件编译使用指南](./条件编译使用指南.md) 获取完整的使用说明。

## 添加新公司

1. 在 `src/conditional-compile/config.js` 中添加公司配置
2. 创建对应的环境配置文件（如 `.env.newcompany`）
3. 在 `package.json` 中添加构建脚本
4. 更新 `vue.config.js` 中的条件编译变量

## 注意事项

- 确保每个 `#ifdef` 都有对应的 `#endif`
- 不要嵌套条件编译块
- 保持公司代码命名的一致性
- 及时测试所有公司版本的构建结果
