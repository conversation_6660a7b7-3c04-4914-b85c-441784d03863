<template>
	<view class="my-page">
		<view class="top-wrap">
			<view class="row">
				<view class="avatar">
					<u-avatar
						size="110" 
						:src="member.profile || ''" 
						:showSex="member.gender != null && member.gender != undefined"
						:sexIcon="member.gender === 'M' ? 'man' : 'woman'"
						bgColor="#fcbd71"
					></u-avatar>
				</view>
				<view class="infos">
					<view class="name">{{ memberName }}</view>
					<view class="phone" @longtap="longtapCopy(member.phone)">{{ member.phone || '-' }}</view>
				</view>
				<view class="user-center">
					<view @click="$navTo('pages/my/userCenter')">用户中心<u-icon name="arrow-right" color="#888888" size="22"></u-icon></view>
				</view>
			</view>
		</view>
		
		
		<view class="menus-top-1">
			<u-grid :col="4" :border="false">
				<!-- <u-grid-item @click="$navTo('pagesMy/event/index?source=my')">
					<text class="icon iconfont icon-lishishijian" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">历史事件</view>
				</u-grid-item> -->
				<u-grid-item @click="$navTo('pagesMy/device/guardian/selector?source=my')">
					<text class="icon iconfont icon-beijianhuren2" style="margin-bottom: 10rpx;color: #20CD8A"></text>
					<view class="grid-text">被监护人</view>
				</u-grid-item>
				<u-grid-item @click="$navTo('pagesMy/device/connector/selector?source=my')">
					<text class="icon iconfont icon-jinjilianxiren3" style="margin-bottom: 10rpx;color: #33BFAE"></text>
					<view class="grid-text">紧急联系人</view>
				</u-grid-item>
				<u-grid-item @click="$navTo('pagesMy/order/index')">
					<text class="icon iconfont icon-dingdan2" style="margin-bottom: 10rpx;color: #FF7E00"></text>
					<view class="grid-text">订单</view>
				</u-grid-item><u-grid-item @click="$navTo('pagesFamily/member-card/activate-card')">
					<text class="icon iconfont icon-huiyuanka" style="margin-bottom: 10rpx; color: #FC7473"></text>
					<view class="grid-text">会员卡</view>
				</u-grid-item>
			</u-grid>
		</view>
		
		<view class="menus-top-2">
			<view class="r-flex tihuo">
				<view class="c-flex card" @click="$navTo(`pagesFamily/member-card/take-delivery-goods-list`)">
					<view class="r-flex">
						<text class="title" >提货</text>
						<text class="icon iconfont icon-qianjin1 jiantou"></text>
					</view>
					<view class="r-flex desc">
						<text class="num">{{cardCount.availableDevNum}}</text>
						<text class="text">台赠送设备</text>
					</view>
				</view>
				<view class="horiz-divider"></view>
				<view class="c-flex card" @click="$navTo(`pagesFamily/member-card/activate-list`)">
					<view class="r-flex">
						<text class="title">延期</text>
						<text class="icon iconfont icon-qianjin1 jiantou"></text>
					</view>
					<view class="r-flex desc">
						<text class="num">{{cardCount.canDelayDevNum-cardCount.delayDevNum}}</text>
						<text class="text">台设备服务</text>
					</view>
				</view>
			</view>
		</view>
		
		<view v-if="spread === '1' && serverTypeList && serverTypeList.length" class="menus-top-2">
			<view class="title-wrap">
				<view class="title">会员服务</view>
			</view>
			<view class="member-server">
				<view class="shouhufuwu" :style="{'background-image': `url(${staticUrl}/images/shouhufuwu_bg.png)`}" @click="gotoBuy">
					<view class="name">
						<text>守护服务</text>
						<text class="icon iconfont icon-qianjin" style="font-size: 36rpx;margin-left:20rpx;margin-bottom: 10rpx; color: white;"></text>
					</view>
					<view class="desc">安全救援+院前垫付</view>
				</view>
				<view class="r-flex server">
					<view class="item" style="flex: 1;" @click="$navTo(`pagesFamily/member-server/my-server?serverId=${worryFree.id}`)">
						<view class="title">
							<text class="icon iconfont icon-wodefuwu1" style="font-size: 36rpx; margin-right:10rpx;margin-bottom: 10rpx;"></text>
							<text>我的服务</text>
						</view>
						<view class="desc">当前{{serverCount}}份会员服务</view>
					</view>
					<view class="item" style="flex: 1;" @click="$navTo(`pagesFamily/member-server/server-record`)">
						<view class="title">
							<text class="icon iconfont icon-dianfujilu" style="font-size: 36rpx; margin-right:10rpx;margin-bottom: 10rpx;"></text>
							<text>垫付记录</text>
						</view>
						<view class="desc">查看记录</view>
					</view>
				</view>
			</view>
			
		</view>
		
		<view v-if="spread === '1' && serverTypeList && serverTypeList.length" class="menus-top-2">
			<view class="title-wrap">
				<view class="title">增值服务</view>
				<view class="more" @click="$navTo('pagesMy/services/index')">全部</view>
			</view>
			<u-grid :col="4" :border="false">
				<u-grid-item v-for="(serverType, index) in serverTypeList" :key="index" @click="$navTo(`pagesMy/serverType/index?id=${serverType.id}&name=${serverType.serverName}`)">
					<!-- <text class="icon iconfont icon-shuaidaojiance" style="margin-bottom: 10rpx;"></text> -->
					<image :src="serverType.serverIcon" style="margin-bottom: 10rpx; height: 70rpx;" mode="heightFix"></image>
					<view class="grid-text">{{ serverType.serverName }}</view>
				</u-grid-item>
				<!-- <u-grid-item>
					<text class="icon iconfont icon-shuaidaojiance" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">摔倒检测</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-jiuzhijiance" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">久滞检测</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-xinshuaijiance" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">心率检测</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-huxijiance" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">呼吸检测</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-yuyintongzhi" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">语音通知</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-duanxintongzhi" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">短信通知</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-renyuanzhuangtai" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">人员状态</view>
				</u-grid-item>
				<u-grid-item>
					<text class="icon iconfont icon-huodongjuli" style="margin-bottom: 10rpx;"></text>
					<view class="grid-text">活动距离</view>
				</u-grid-item> -->
			</u-grid>
		</view>
		
		
		<view class="item-block">
			<u-row @click="$navTo('pagesMy/message/index')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-xitongxiaoxi" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle; position: relative; top: 6rpx;"></text>
						<view class="field-name" style="position: relative; top: 6rpx;">系统消息</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-tag v-if="noReadSysMsgCount > 0" :text="noReadSysMsgCount" mode="dark" type="error" shape="circle" />
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row @click="$navTo('pagesMy/telephone/telephone')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-dingwei" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle;"></text>
						<view class="field-name">智能固话绑定/解绑</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row @click="$navTo('pagesMy/help/index?key=help')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-bangzhuzhongxin" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle; position: relative; top: 2rpx;"></text>
						<view class="field-name" style="position: relative; top: 6rpx;">帮助中心</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row @click="$navTo('pagesMy/address/index')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-dingwei" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle;"></text>
						<view class="field-name">收货地址</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row @click="$navTo('pagesMy/authorize/index')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-sqgl" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle;"></text>
						<view class="field-name">授权管理</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-tag v-if="unauthorizedCount > 0" :text="unauthorizedCount" mode="dark" type="error" shape="circle" />
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row @click="$navTo('pagesMy/feedback/index')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-ico_jiudianguanli_shebeiweixiudengji1" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle;"></text>
						<view class="field-name">问题反馈</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row @click="$navTo('pages/my/setting')">
				<u-col span="8">
					<view class="left-info">
						<text class="icon iconfont icon-shezhi" style="color: #01B09A; margin-right: 10rpx; vertical-align: middle;"></text>
						<view class="field-name">设置</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="26"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view style="height: 10rpx;"></view>
		
		<u-modal ref="createFamilyDialog" v-model="createFamilyDialog.show" title="创建家庭" :async-close="true" @confirm="handleCreateFamily">
			<view class="slot-content" style="padding: 20rpx; margin-top: 20rpx;">
				<u-input v-model="createFamilyDialog.ipt" type="text" :border="true" placeholder="我的家" :maxlength="15" />
			</view>
		</u-modal>
		
	</view>
</template>

<script>
	import {longtapCopy} from '../../utils/util'
	import {subscribeMessage} from '@/utils/WXSubscribeMessage.js'
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				member: {},
				contacts: [],
				serverTypeList: [],
				noReadSysMsgCount: 0,
				unauthorizedCount: 0,
				spread: false,
				createFamilyDialog: {
					show: false,
					ipt: '',
				},
				worryFree: null,
				serverCount:0,
				cardCount:{
					deviceNum:0,
					canDelayDevNum:0,
					availableDevNum:0,
					delayDevNum:0,
				},
			}
		},
		computed: { 
			memberName() {
				const name = this.member?.name;
				if (!name) {
					return "-";
				}
				if (name.length > 5) {
					return name.substring(0, 5) + "...";
				}
				return name;
			}
		},
    onLoad() {//默认加载
            // this.login();
			//#ifdef MP-WEIXIN
			wx.hideHomeButton()
			//#endif
    },
		async onShow() {
			this.$u.api.fetchMemberDetailInfo({ }).then(res => {
				this.member = res;
			})
			this.fetchFamily();
			this.fetchDatas();
			this.fetchNoReadSysMsgCount();
			this.getUnauthorizedCount();
			this.queryCardCount();
			this.fetchOrgSpreadServer();
			try {
				this.worryFree = await this.$u.api.getServerByType();
			} catch {
				this.worryFree = null;
			}
			try {
				this.serverCount = await this.$u.api.countMemberServerCurrent({serverId:this.worryFree.id});
			} catch {
				this.serverCount = 0;
			}
		},
		methods: {
			longtapCopy,
			gotoBuy(){
				subscribeMessage(["Kj9MF9UOFgoWP1jE8-cX-pi5LcX_CJVXrSHFqx9niR0"]);
				if(this.worryFree && this.worryFree.validStatus=='0'){
					this.$navTo(`pagesFamily/device/spec/buy?source=wyfw&serverId=${this.worryFree.id}`)
				}else{
					uni.showToast({
						duration: 2000,
						title: '服务已禁用，请稍后再试',
						icon: 'none'
					})
				}
			},
			fetchFamily() {
				this.$u.api.fetchFamilyList({ }).then(res => {
					this.createFamilyDialog.ipt = '';
					if (!res || !res.length) {
						this.createFamilyDialog.show = true;
					} else {
						this.createFamilyDialog.show = false;
					}
				})
			},
			handleCreateFamily() {
				if (!this.createFamilyDialog.ipt) {
					this.createFamilyDialog.ipt = '我的家'
					// this.$toast('请填写家庭名称');
					// this.$refs.createFamilyDialog.clearLoading();
					// return;
				}
				this.$u.api.execCreateFamily({ name: this.createFamilyDialog.ipt }).then(res => {
					this.createFamilyDialog.ipt = '';
					this.createFamilyDialog.show = false;
					this.$toast('创建家庭成功');
					uni.reLaunch({
						url: 'pages/index/index'
					})
				}).catch(err => {
					this.$refs.createFamilyDialog.clearLoading();
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			fetchDatas() {
				this.$u.api.fetchContactMyList({ }).then(res => {
					this.contacts = res
				})
			},
			fetchNoReadSysMsgCount() {
				this.$u.api.fetchNoReadSysMsgCount({ }).then(res => {
					this.noReadSysMsgCount = res || 0
				})
			},
			getUnauthorizedCount() {
				this.$u.api.getUnauthorizedCount({ }).then(res => {
					this.unauthorizedCount = res || 0
				})
			},
			queryCardCount() {
				this.$u.api.queryCardCount({ }).then(res => {
					if(res){
						this.cardCount = res
					}
				})
			},
			fetchOrgSpreadServer() {
				this.$u.api.fetchOrgSpreadServer({ }).then(res => {
					this.spread = res;
					if (res === '1') {
						this.fetchServerList();
					}
				})
			},
			fetchServerList() {
				this.$u.api.fetchServerList({ }).then(res => {
					this.serverTypeList = (res || []).slice(0, 8)
				})
			},
			
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F7F7F7;
	// #ifdef H5
	// padding-bottom: 50rpx;
	overflow: auto;
	//#endif
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.my-page {
	// height: 100vh;
	// overflow: hidden;
	//#ifdef H5
	// height: 100%;
	//#endif
	.tihuo{
		justify-content: space-evenly;
		.card{
			.title{
				font-size: 32rpx;
				line-height: 30rpx;
				font-weight: 700;
				color: #333333;
			}
			.jiantou{
				font-size: 32rpx;
				color: #999999;
			}
			.desc{
					margin-top: 32rpx;
					align-items: baseline;
				.num{
					line-height: 31rpx;
					font-size: 44rpx;
					font-weight: 700;
					color: #01b09a;
				}
				.text{
					margin-left: 8rpx;
					font-size: 26rpx;
					line-height: 31rpx;
					color: #999999;
				}
			}
		}
		.horiz-divider{
			margin: 10rpx 0 10rpx 4rpx;
			background-color: #eeeeee;
			width: 2rpx;
			height: 96rpx;
		}
	}
	.top-wrap {
		// background: url(../../static/images/my-top-bg.png) center center no-repeat;
		// background-size: 100% 100%;
		// height: 330upx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		justify-items: center;
		padding: 40rpx 0rpx 20rpx 0rpx;
		//#ifdef H5
		// height: 230upx;
		//#endif
		.row {
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 0 34upx;
			.avatar {
				width: 140upx;
			}
			.infos {
				flex: 1;
				color: black;
				.name {
					font-size: 32upx;
					margin-bottom: 20upx;
					font-weight: bold;
				}
				.phone {
					font-size: 26upx;
					margin-bottom: 14rpx;
					color: #888888;
				}
			}
			.user-center {
				width: 140upx;
				color: #888888;
				font-size: 26upx;
				text-align: right;
			}
		}
	}
	
	.menus-top-1 {
		background: #fff;
		padding: 22upx 18upx;
		box-sizing: border-box;
		margin: 20rpx 20rpx;
		border-radius: 16rpx;
		.icon {
			font-size: 80rpx;
			//color: #01B09A;
		}
		.grid-text {
			color: #888888;
			font-size: 25rpx;
			margin-top: 10rpx;
		}
	}
	.member-server {
		.shouhufuwu{
			padding: 20rpx;
			width: 700rpx;
			height: 200rpx;
			background-size: 700rpx 200rpx;
			background-repeat:no-repeat;
			color: white;
			.name{
				margin-top: 30rpx;
				padding-left: 40rpx;
				font-size: 32rpx;
			}
			.desc{
				margin-top: 20rpx;
				padding-left: 40rpx;
				font-size: 26rpx;
			}
		}
		.server{
			//padding: 20rpx;
			width: 700rpx;
			height: 140rpx;
			.item{
				margin: 20rpx;
				height: 120rpx;
				padding-left: 40rpx;
				border-radius: 20rpx;
				background-size: 700rpx 140rpx;
				background-color: rgba(245,247,251,0.39);
				.title{
					margin-top: 10rpx;
					color: #01B09A;
					font-size: 32rpx;
				}
				.desc{
					margin-top: 10rpx;
					color: #999999;
					font-size: 26rpx;
				}
			}
		}
		
	}
	
	.menus-top-2 {
		background: #fff;
		padding: 22upx 6upx;
		box-sizing: border-box;
		margin: 20rpx 20rpx;
		border-radius: 16rpx;
		padding-top: 32rpx;
		.title-wrap {
			display: flex;
			flex-direction: row;
			margin-bottom: 20rpx;
			padding: 0rpx 30rpx;
			.title {
				flex: 1;
				font-size: 32rpx;
				// font-weight: bold;
				color: #000;
			}
			.more {
				width: 200rpx;
				font-size: 30rpx;
				color: #888;
				text-align: right;
			}
		}
		.icon {
			color: #08d9ce;
			font-size: 60rpx;
		}
		.grid-text {
			color: #000;
			font-size: 26rpx;
			margin-top: 10rpx;
		}
	}
	
	
	.content {
		// margin-top: 20upx;
		background: #fff;
		height: 870upx;
		padding: 22upx 26upx;
		box-sizing: border-box;
		.title-wrap {
			position: relative;
			.title {
				font-size: 34upx;
				color: rgba(0,0,0,0.65);
				text-align: left;
				font-weight: bold;
			}
			.right-more {
				position: absolute;
				top: 1upx;
				right: 0upx;
				font-size: 32upx;
				color: rgba(0,0,0,0.45);
			}
		}
		.cards {
			margin-top: 20upx;
			margin-bottom: 20upx;
			display: flex;
			// flex-direction: row;
			.card-item {
				flex: 0 0 220upx;
				// height: 254upx;
				padding: 30upx 0upx;
				background: #FFFFFF;
				text-align: center;
				box-shadow: 0px 0px 12upx 2upx rgba(186,184,184,0.32);
				border-radius: 14px;
				margin-left: 20upx;
				box-sizing: border-box;
				.name {
					margin-top: 14upx;
				}
				.relationship {
					padding: 10upx 10upx;
					background: #DDE3F9;
					border-radius: 18upx;
					margin: 0upx 30upx;
					margin-top: 10upx;
				}
				// &:nth-child(n + 2) {
				// 	margin-left: 20upx;
				// }
				&:nth-last-child(1) {
					margin-right: 20upx;
				}
			}
		}
	}
	
	.item-block {
		// height: 784rpx;
		background: white;
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		padding-top: 0rpx;
		margin: 20rpx 20rpx;
		u-row {
			display: block;
			line-height: 60rpx;
			padding: 18rpx 0;
		}
		//#ifdef H5
		line-height: 50px;
		//#endif
		.left-info {
			.icon {
				font-size: 36rpx;
			}
			image {
				width: 36rpx;
				height: 36rpx;
				display: inline-block;
				margin-right: 20rpx;
				vertical-align: middle;
			}
			.field-name {
				display: inline-block;
				color: #000;
				font-size: 32rpx;
				position: relative;
				top: 2rpx;
				// font-weight: bold;
				margin-left: 16rpx;
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			// margin-top: 28rpx;
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: 2rpx;
			}
			::v-deep .circle-tip-num {
				display: inline-block;
				width: 30rpx;
				height: 30rpx;
				border-radius: 30rpx;
				text-align: center;
				background: red;
				color: white;
			}
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
}
</style>
