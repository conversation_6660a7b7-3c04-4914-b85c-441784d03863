/**
 * 条件编译模块统一导出
 * 
 * 使用方式：
 * import { isCompany, getCurrentCompanyConfig, conditionalMixin } from '@/conditional-compile'
 * 
 * <AUTHOR>
 */

// 导出配置相关
export {
  SUPPORTED_COMPANIES,
  CURRENT_COMPANY,
  COMPANY_CONFIGS,
  getCurrentCompanyConfig,
  isCompany,
  conditional
} from './config'

// 导出工具函数
export {
  renderByCompany,
  executeByCompany,
  getClassByCompany,
  getTextByCompany,
  getConfigByCompany,
  isFeatureEnabled,
  getThemeConfig,
  getApiConfig,
  conditionalMixin
} from './utils'

// 默认导出
export { default as config } from './config'
export { default as utils } from './utils'
