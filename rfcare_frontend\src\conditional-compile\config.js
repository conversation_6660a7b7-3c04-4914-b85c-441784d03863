/**
 * 条件编译配置文件
 * 用于管理不同公司的编译条件和配置
 * 
 * 使用方式类似 uniapp 的条件编译：
 * // #ifdef COMPANY_BOE
 * 该公司特有的代码
 * // #endif
 * 
 * <AUTHOR>
 */

// 支持的公司平台列表
export const SUPPORTED_COMPANIES = {
  BOE: 'boe',           // 京东方
  DEFAULT: 'default'    // 默认配置
}

// 当前编译的公司平台
export const CURRENT_COMPANY = process.env.VUE_APP_COMPANY || SUPPORTED_COMPANIES.DEFAULT

// 公司特定配置
export const COMPANY_CONFIGS = {
  [SUPPORTED_COMPANIES.BOE]: {
    name: '京东方',
    code: 'boe',
    theme: {
      primaryColor: '#1890ff',
      logoUrl: '/images/logo-boe.png'
    },
    features: {
      enableAdvancedReports: true,
      enableCustomDashboard: true,
      enableMultiTenant: false
    },
    api: {
      baseUrl: '/api',
      wsUrl: 'wss://boe.rfcare.cn'
    }
  },
  [SUPPORTED_COMPANIES.DEFAULT]: {
    name: '与安服务管理系统',
    code: 'default',
    theme: {
      primaryColor: '#409EFF',
      logoUrl: '/images/logo.png'
    },
    features: {
      enableAdvancedReports: false,
      enableCustomDashboard: false,
      enableMultiTenant: true
    },
    api: {
      baseUrl: '/api',
      wsUrl: 'wss://www.rfcare.cn'
    }
  }
}

// 获取当前公司配置
export const getCurrentCompanyConfig = () => {
  return COMPANY_CONFIGS[CURRENT_COMPANY] || COMPANY_CONFIGS[SUPPORTED_COMPANIES.DEFAULT]
}

// 检查是否为指定公司
export const isCompany = (companyCode) => {
  return CURRENT_COMPANY === companyCode
}

// 条件编译辅助函数
export const conditional = {
  // 检查是否为 BOE 公司
  isBOE: () => isCompany(SUPPORTED_COMPANIES.BOE),
  
  // 检查是否为默认配置
  isDefault: () => isCompany(SUPPORTED_COMPANIES.DEFAULT),
  
  // 根据公司返回不同的值
  byCompany: (configs) => {
    return configs[CURRENT_COMPANY] || configs[SUPPORTED_COMPANIES.DEFAULT] || null
  }
}

export default {
  SUPPORTED_COMPANIES,
  CURRENT_COMPANY,
  COMPANY_CONFIGS,
  getCurrentCompanyConfig,
  isCompany,
  conditional
}
