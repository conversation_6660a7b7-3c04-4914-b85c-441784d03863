---
description: A description of your rule
---

# 目录介绍
当前工作空间下面有多个不同语言开发的项目，为了能够跨项目进行代码参考和功能实现，将他们放到同一个目录下。执行命令时，需要先进入对应的项目目录下，这个非常重要。Windows环境下命令不支持 && |等符号，请以此执行一个命令。
路径为：D:\workspace\sxkj\ai_ecare
操作系统：Windows10
下面是本工作空间下的目录结构：
- wadar_ios:为wadar的iOS项目
- yuan_app:为与安宝的小程序、h5项目后称小程序或h5都指此项目。该项目无法使用命令行启动，必须使用Hbulder X这个ide，所以该项目可以不用测试
- ecare-android:为ecare的Android项目，编译命令：cmd /c "cd ecare-android && gradle wrapper"


# Guidelines
## 通用标准
### 禁止项
  - 在可能出现异常的地方禁止吞掉异常。
  - 注释掉的代码你不要删除。
  - 已经有的日志输出不要删除。
  - 若无用户要求兼容则禁止向后兼容。
  - 涉及到图标的，不要生成图片，要使用自带图标或iconfont，如果本地没有的使用占位符代替（隐含禁止生成图片）。
  - 问题解决不了时，坚决禁止屏蔽功能。
  - 没找到真正错误原因并解决前，不得使用重试机制。
### 必须项
  - 使用中文回复消息、写注释、文档等。
  - 执行代码生成或修改前，先确定需要修改的项目、目录、文件。
  - 使用当前项目的代码风格，包括文件夹/包、文件、类、方法、变量等的命名习惯，依赖的库和写法。
  - 生成代码时使用该语言、框架、平台的最佳实践，兼顾代码的性能、健壮性、安全性、可读性。
  - 新增代码尽可能放到现有符合逻辑的文件夹。
  - 代码文件中尽量不要超过800行，超过则拆分代码文件，保持结构清晰。
  - 项目中超过20行代码重复的地方请单独抽取出方法。
  - 所有类/接口/文件、方法都需要有注释，方法需有用途、参数和返回值注释。
  - 为生成的代码添加中文文档注释，解释代码的作用。
  - 魔法数字使用枚举或定义常量代替。
  - 使用完整的单词而不是缩写。
  - 生成的说明文档放到项目的docs文件夹下，文件名使用为中文。
  - 在代码没做编译、测试之前不要结束任务。
  - 启动应用时需要监控日志，如果发现错误，需自动解决。
  - 在可能出现异常的地方必须增加错误日志。
  - 除非简单任务，不然都以任务列表的方式组织工作。
  - 生成代码后自动格式化。
  - 如果要代码署名作者，请署名guan。
  - 出现问题要找到真正原因并解决。
  - 重试或降级方案应该询问用户，得到明确答复后再实现。
  - 问题没解决或方案变更后，恢复到本次修改前的代码。
  - 删除代码前，检查所有引用，先确定能否删除，可以删除时同步修改引用处。
  - 用户提出的解决方案不是业界主流和最优解决方案时，提醒用户并使用最优解决方案。
  - 重构代码后需要删除旧的代码。
  - 修改或生成代码前查看当前项目依赖的版本，按依赖中指定版本写代码。
  - 每进入一个新的页面或流程节点需要有日志输出，在监控日志时根据这个日志判断流程是否正常。
  - 任务完成的标志是编译无误且监控日志中相关功能没有异常或错误，不然不能结束任务。
  - 问题解决不了时，应该尝试更多办法或向用户获取更多错误信息。
  - 开始日志监控时只关注当前时间之后到日志，禁止以之前到日志作为参考和成功、失败的判断。

## Java项目
### 禁止项
  - 禁止Lambda嵌套。
  - 禁止泛型嵌套、三目运算嵌套。
  - 有接口可调用的，禁止用反射。
### 必须项
  - 少用链式调用，调用时也不要超过3次。
  - 尽量少用Lambda表达式。
  - 少用内部类。
  - 使用显示判断，不用Optional判断null。
  - 条件语句中不要省略花括号。

## 前端项目
#### 基本要求
### 禁止项
  - 避免使用类（隐含禁止使用类）。
  - 禁止回掉地狱。
  - 不用npx（新建项目时）。
### 必须项
  - 使用函数式、声明式编程。
  - 尽量不要使用内联样式，除非无法重用且样式属性少于3。
  - 对现有项目检查项目使用项目中正在使用的包管理器。
  - 对于新建项目，优先级依次为：pnpm、yarn、npm。
  - 新建项目不手动创建，要使用官方脚手架。
  - 使用Jest进行单元测试。
  - 使用Playwright测试，检查控制台错误、页面错误、请求错误，需要时截图来验证。
  - 需作防抖节流。
  - Promise 需要catch。
  - 组件创建时需遵循SOLID设计原则。
  - 组件需要满足可维护、可扩展、高复用性。
  - 创建的组件需要有使用说明文档，与组件同目录。
  - 条件语句中不要省略花括号。

#### ReactJS项目
### 禁止项
  - 不使用React.FC和Class。
### 必须项
  - 组件使用箭头函数赋值组件。

#### VueJS项目
- （无具体规则，因此无分类内容）

#### 样式
### 禁止项
  - 少用 !important（隐含避免使用）。
### 必须项
  - 通过提升选择器优先级来替代 !important。

## iOS项目
### 禁止项
  - 使用显示判断，而不用guard（隐含禁止使用guard）。
  - 条件语句中不进行赋值。
### 必须项
  - 使用Swift和SwiftUI进行开发。
  - 使用MVVM设计模式进行目录组织和代码开发。
  - 新建Swift代码文件后，加入到project.pbxproj文件中。
  - 项目使用workspace而不是project来编译。
  - 编译完后使用命令行启动模拟器，并监控日志，如有错误或未能实现功能需求，自动调整代码。
  - 默认使用Test Scheme编译运行。
  - 保持项目现有的Info.plist策略，不要私自新建Info.plist文件。

## Android项目
### 禁止项
  ### 禁止项
### 必须项
  - 使用Java和xml进行开发。
  - 使用MVVM设计模式进行目录组织和代码开发。
  - 数据展现为主的的页面使用ViewBinding。
  - 表单页面使用DataBinding。
  - 使用系统现有的jdk、gradle、sdk进行编译和测试，不兼容除外。
  - 完成功能后需要编译和测试，并持续跟踪日志和根据日志修复错误。
  - 写完代码后，都进行编译、打包/安装、启动，并跟踪日志。