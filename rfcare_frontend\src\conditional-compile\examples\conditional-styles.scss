/**
 * 条件编译样式示例
 * 展示如何在 SCSS 文件中使用条件编译
 *
 * <AUTHOR>
 */

// 通用样式变量
$base-font-size: 14px;
$base-line-height: 1.5;
$border-radius: 4px;

// #ifdef COMPANY_BOE
// BOE 公司特有的样式变量
$boe-primary-color: #1890ff;
$boe-secondary-color: #40a9ff;
$boe-success-color: #52c41a;
$boe-warning-color: #faad14;
$boe-error-color: #f5222d;

// BOE 主题样式
.boe-theme {
    --primary-color: #{$boe-primary-color};
    --secondary-color: #{$boe-secondary-color};
    --success-color: #{$boe-success-color};
    --warning-color: #{$boe-warning-color};
    --error-color: #{$boe-error-color};
    .header {
        background: linear-gradient(90deg, $boe-primary-color, $boe-secondary-color);
        color: white;
        .logo {
            background-image: url('/images/logo-boe.png');
        }
    }
    .sidebar {
        background-color: #001529;
        .menu-item {
            &:hover {
                background-color: $boe-primary-color;
            }
            &.active {
                background-color: $boe-secondary-color;
            }
        }
    }
    .button {
        &.primary {
            background-color: $boe-primary-color;
            border-color: $boe-primary-color;
            &:hover {
                background-color: $boe-secondary-color;
                border-color: $boe-secondary-color;
            }
        }
    }
    .card {
        border: 1px solid lighten($boe-primary-color, 30%);
        box-shadow: 0 2px 8px rgba($boe-primary-color, 0.1);
    }
}

// BOE 特有的组件样式
.boe-dashboard {
    .widget {
        background:
            linear-gradient(
                135deg,
                lighten($boe-primary-color, 40%),
                lighten($boe-secondary-color, 40%)
            );
        border-left: 4px solid $boe-primary-color;
    }
    .chart-container {
        .chart-title {
            color: $boe-primary-color;
            font-weight: bold;
        }
    }
}
.boe-form {
    .form-item {
        .label {
            color: $boe-primary-color;
        }
        .input {
            border-color: lighten($boe-primary-color, 20%);
            &:focus {
                border-color: $boe-primary-color;
                box-shadow: 0 0 0 2px rgba($boe-primary-color, 0.2);
            }
        }
    }
}
// #endif

// #ifdef COMPANY_DEFAULT
// 默认公司的样式变量
$default-primary-color: #409eff;
$default-secondary-color: #67c23a;
$default-success-color: #67c23a;
$default-warning-color: #e6a23c;
$default-error-color: #f56c6c;

// 默认主题样式
.default-theme {
    --primary-color: #{$default-primary-color};
    --secondary-color: #{$default-secondary-color};
    --success-color: #{$default-success-color};
    --warning-color: #{$default-warning-color};
    --error-color: #{$default-error-color};
    .header {
        background: linear-gradient(90deg, $default-primary-color, $default-secondary-color);
        color: white;
        .logo {
            background-image: url('/images/logo.png');
        }
    }
    .sidebar {
        background-color: #304156;
        .menu-item {
            &:hover {
                background-color: $default-primary-color;
            }
            &.active {
                background-color: $default-secondary-color;
            }
        }
    }
    .button {
        &.primary {
            background-color: $default-primary-color;
            border-color: $default-primary-color;
            &:hover {
                background-color: lighten($default-primary-color, 10%);
                border-color: lighten($default-primary-color, 10%);
            }
        }
    }
    .card {
        border: 1px solid lighten($default-primary-color, 30%);
        box-shadow: 0 2px 8px rgba($default-primary-color, 0.1);
    }
}

// 默认版本的组件样式
.default-dashboard {
    .widget {
        background:
            linear-gradient(
                135deg,
                lighten($default-primary-color, 40%),
                lighten($default-secondary-color, 40%)
            );
        border-left: 4px solid $default-primary-color;
    }
    .chart-container {
        .chart-title {
            color: $default-primary-color;
            font-weight: bold;
        }
    }
}
.default-form {
    .form-item {
        .label {
            color: $default-primary-color;
        }
        .input {
            border-color: lighten($default-primary-color, 20%);
            &:focus {
                border-color: $default-primary-color;
                box-shadow: 0 0 0 2px rgba($default-primary-color, 0.2);
            }
        }
    }
}
// #endif

// 通用样式（所有版本都有）
.common-layout {
    font-size: $base-font-size;
    line-height: $base-line-height;
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -10px;
    }
    .col {
        padding: 0 10px;
        flex: 1;
    }
}
.common-components {
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }
    .error-message {
        color: var(--error-color);
        padding: 10px;
        border: 1px solid var(--error-color);
        border-radius: $border-radius;
        background-color: rgba(var(--error-color), 0.1);
    }
    .success-message {
        color: var(--success-color);
        padding: 10px;
        border: 1px solid var(--success-color);
        border-radius: $border-radius;
        background-color: rgba(var(--success-color), 0.1);
    }
}

// 响应式样式
@media (max-width: 768px) {
    .common-layout {
        .container {
            padding: 0 10px;
        }
        .row {
            margin: 0 -5px;
        }
        .col {
            padding: 0 5px;
        }
    }

    // #ifdef COMPANY_BOE
    .boe-theme {
        .sidebar {
            width: 100%;
            height: auto;
        }
    }
    // #endif

    // #ifdef COMPANY_DEFAULT
    .default-theme {
        .sidebar {
            width: 100%;
            height: auto;
        }
    }
    // #endif
}
