# 条件编译使用指南

## 概述

本项目实现了类似 uniapp 的条件编译功能，支持为不同公司进行私有化部署时的代码差异化处理。通过条件编译，可以在同一套代码中为不同的公司版本提供不同的功能、样式和配置。

## 功能特性

- 🎯 **编译时条件编译**：使用注释语法，在构建时移除不需要的代码
- 🔄 **运行时条件编译**：使用 JavaScript 函数，在运行时动态判断
- 🎨 **样式条件编译**：支持 CSS/SCSS 的条件编译
- 📦 **组件条件编译**：支持 Vue 组件的条件编译
- ⚙️ **配置管理**：统一管理不同公司的配置信息
- 🛠️ **开发工具**：提供便捷的工具函数和混入

## 支持的公司

目前支持以下公司版本：

- **BOE**：京东方版本（`boe`）
- **DEFAULT**：默认版本（`default`）

## 构建命令

### 开发环境

```bash
# 默认版本开发环境
yarn start

# BOE 版本开发环境
yarn start:boe
```

### 生产环境

```bash
# 默认版本生产构建
yarn build

# BOE 版本生产构建
yarn build:boe

# 测试环境构建
yarn build:test
```

## 条件编译语法

### 1. JavaScript/TypeScript 文件

使用注释语法进行条件编译：

```javascript
// 通用代码（所有版本都包含）
const commonFunction = () => {
  console.log('这是通用功能')
}

// #ifdef COMPANY_BOE
// BOE 公司特有的代码
const boeSpecialFunction = () => {
  console.log('BOE 特有功能')
}
// #endif

// #ifdef COMPANY_DEFAULT
// 默认版本特有的代码
const defaultFunction = () => {
  console.log('默认版本功能')
}
// #endif

// 条件导出
export default {
  commonFunction,
  // #ifdef COMPANY_BOE
  boeSpecialFunction,
  // #endif
  // #ifdef COMPANY_DEFAULT
  defaultFunction
  // #endif
}
```

### 2. Vue 组件

#### 模板条件编译

```vue
<template>
  <div class="component">
    <!-- 通用内容 -->
    <h1>{{ title }}</h1>
    
    <!-- BOE 版本特有内容 -->
    <!-- #ifdef COMPANY_BOE -->
    <div class="boe-section">
      <p>BOE 专属功能</p>
      <el-button type="primary">BOE 按钮</el-button>
    </div>
    <!-- #endif -->
    
    <!-- 默认版本特有内容 -->
    <!-- #ifdef COMPANY_DEFAULT -->
    <div class="default-section">
      <p>默认版本功能</p>
      <el-button type="success">默认按钮</el-button>
    </div>
    <!-- #endif -->
    
    <!-- 运行时条件编译 -->
    <div v-if="$isBOE()">运行时 BOE 内容</div>
    <div v-if="$isDefault()">运行时默认内容</div>
  </div>
</template>
```

#### 脚本条件编译

```vue
<script>
import { conditionalMixin } from '@/conditional-compile'

export default {
  name: 'ExampleComponent',
  mixins: [conditionalMixin],
  data() {
    return {
      title: '示例组件'
    }
  },
  methods: {
    // #ifdef COMPANY_BOE
    boeMethod() {
      this.$message.success('BOE 方法执行')
    },
    // #endif
    
    // #ifdef COMPANY_DEFAULT
    defaultMethod() {
      this.$message.success('默认方法执行')
    },
    // #endif
    
    handleClick() {
      // 运行时条件判断
      if (this.$isBOE()) {
        this.boeMethod()
      } else if (this.$isDefault()) {
        this.defaultMethod()
      }
    }
  }
}
</script>
```

### 3. 样式条件编译

#### SCSS 文件

```scss
// 通用样式
.common-class {
  padding: 20px;
  margin: 10px;
}

/* #ifdef COMPANY_BOE */
// BOE 特有样式
.boe-theme {
  --primary-color: #1890ff;
  
  .header {
    background: linear-gradient(90deg, #1890ff, #40a9ff);
  }
  
  .button {
    background-color: #1890ff;
    border-color: #1890ff;
  }
}
/* #endif */

/* #ifdef COMPANY_DEFAULT */
// 默认版本样式
.default-theme {
  --primary-color: #409EFF;
  
  .header {
    background: linear-gradient(90deg, #409EFF, #67C23A);
  }
  
  .button {
    background-color: #409EFF;
    border-color: #409EFF;
  }
}
/* #endif */
```

#### Vue 组件样式

```vue
<style scoped>
.component {
  padding: 20px;
}

/* #ifdef COMPANY_BOE */
.boe-section {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}
/* #endif */

/* #ifdef COMPANY_DEFAULT */
.default-section {
  background-color: #f0f9ff;
  border: 1px solid #409EFF;
}
/* #endif */
</style>
```

## 运行时条件编译

### 使用混入

```vue
<script>
import { conditionalMixin } from '@/conditional-compile'

export default {
  mixins: [conditionalMixin],
  methods: {
    handleAction() {
      // 检查是否为 BOE 版本
      if (this.$isBOE()) {
        console.log('BOE 版本逻辑')
      }
      
      // 检查是否为默认版本
      if (this.$isDefault()) {
        console.log('默认版本逻辑')
      }
      
      // 根据公司返回不同值
      const config = this.$byCompany({
        boe: { color: '#1890ff', name: 'BOE' },
        default: { color: '#409EFF', name: '默认' }
      })
      
      // 检查功能是否启用
      if (this.$isFeatureEnabled('enableAdvancedReports')) {
        console.log('高级报表功能已启用')
      }
    }
  }
}
</script>
```

### 使用工具函数

```javascript
import { 
  isCompany, 
  getCurrentCompanyConfig, 
  isFeatureEnabled,
  getThemeConfig 
} from '@/conditional-compile'

// 检查当前公司
if (isCompany('boe')) {
  console.log('当前是 BOE 版本')
}

// 获取当前公司配置
const config = getCurrentCompanyConfig()
console.log('公司名称:', config.name)
console.log('主题色:', config.theme.primaryColor)

// 检查功能开关
if (isFeatureEnabled('enableCustomDashboard')) {
  console.log('自定义仪表板功能已启用')
}

// 获取主题配置
const theme = getThemeConfig()
console.log('Logo URL:', theme.logoUrl)
```

## 配置管理

### 环境配置文件

项目支持以下环境配置文件：

- `.env.development` - 默认开发环境
- `.env.production` - 默认生产环境
- `.env.test` - 测试环境
- `.env.boe` - BOE 生产环境
- `.env.boe.development` - BOE 开发环境

### 公司配置

在 `src/conditional-compile/config.js` 中管理公司配置：

```javascript
export const COMPANY_CONFIGS = {
  boe: {
    name: '京东方',
    code: 'boe',
    theme: {
      primaryColor: '#1890ff',
      logoUrl: '/images/logo-boe.png'
    },
    features: {
      enableAdvancedReports: true,
      enableCustomDashboard: true,
      enableMultiTenant: false
    },
    api: {
      baseUrl: '/api',
      wsUrl: 'wss://boe.rfcare.cn'
    }
  }
}
```

## 最佳实践

### 1. 代码组织

- 将条件编译相关的代码放在 `src/conditional-compile` 目录下
- 使用统一的配置管理，避免硬编码
- 优先使用编译时条件编译，减少运行时开销

### 2. 命名规范

- 公司代码使用小写字母，如 `boe`、`default`
- 环境变量使用 `COMPANY_` 前缀，如 `COMPANY_BOE`
- 配置文件使用公司代码作为后缀，如 `.env.boe`

### 3. 功能开关

- 使用 `features` 配置管理功能开关
- 通过 `isFeatureEnabled()` 函数检查功能状态
- 避免在多个地方重复判断同一个功能

### 4. 样式管理

- 使用 CSS 变量管理主题色
- 将公司特有样式放在对应的条件编译块中
- 保持样式的一致性和可维护性

## 添加新公司

### 1. 添加公司配置

在 `src/conditional-compile/config.js` 中添加新公司：

```javascript
export const SUPPORTED_COMPANIES = {
  BOE: 'boe',
  NEW_COMPANY: 'newcompany',  // 新增
  DEFAULT: 'default'
}

export const COMPANY_CONFIGS = {
  // ... 现有配置
  newcompany: {
    name: '新公司',
    code: 'newcompany',
    theme: {
      primaryColor: '#ff6b6b',
      logoUrl: '/images/logo-newcompany.png'
    },
    features: {
      enableAdvancedReports: false,
      enableCustomDashboard: true,
      enableMultiTenant: true
    },
    api: {
      baseUrl: '/api',
      wsUrl: 'wss://newcompany.example.com'
    }
  }
}
```

### 2. 创建环境配置文件

创建 `.env.newcompany` 和 `.env.newcompany.development` 文件。

### 3. 添加构建脚本

在 `package.json` 中添加构建命令：

```json
{
  "scripts": {
    "start:newcompany": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --mode newcompany.development",
    "build:newcompany": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode newcompany --dest dist-newcompany"
  }
}
```

### 4. 更新 webpack 配置

在 `vue.config.js` 中添加新公司的条件编译变量：

```javascript
const conditionalCompileOptions = {
  isDebug: process.env.NODE_ENV === 'development',
  envVars: {
    COMPANY_BOE: company === 'boe',
    COMPANY_NEWCOMPANY: company === 'newcompany',  // 新增
    COMPANY_DEFAULT: company === 'default'
  }
}
```

### 5. 使用新的条件编译

```javascript
// #ifdef COMPANY_NEWCOMPANY
const newCompanyFunction = () => {
  console.log('新公司特有功能')
}
// #endif
```

## 注意事项

1. **条件编译块必须完整**：确保每个 `#ifdef` 都有对应的 `#endif`
2. **避免嵌套**：不要在条件编译块内嵌套其他条件编译块
3. **保持一致性**：在所有文件中使用相同的公司代码和命名规范
4. **测试覆盖**：确保每个公司版本都经过充分测试
5. **文档更新**：添加新公司时及时更新相关文档

## 故障排除

### 常见问题

1. **构建失败**：检查条件编译语法是否正确，确保 `#ifdef` 和 `#endif` 配对
2. **功能不生效**：检查环境变量 `VUE_APP_COMPANY` 是否正确设置
3. **样式不显示**：检查 CSS 条件编译语法，确保使用正确的注释格式
4. **运行时错误**：检查是否正确导入和使用条件编译工具函数

### 调试技巧

1. 在浏览器控制台查看 `process.env.VUE_APP_COMPANY` 确认当前公司
2. 使用 `getCurrentCompanyConfig()` 查看当前配置
3. 检查构建输出，确认条件编译是否正确移除了不需要的代码

## 快速开始

### 1. 创建 BOE 版本的页面

```vue
<template>
  <div class="example-page">
    <h1>{{ pageTitle }}</h1>

    <!-- #ifdef COMPANY_BOE -->
    <div class="boe-banner">
      <img src="/images/logo-boe.png" alt="BOE Logo">
      <p>欢迎使用 BOE 服务管理系统</p>
    </div>
    <!-- #endif -->

    <!-- #ifdef COMPANY_DEFAULT -->
    <div class="default-banner">
      <img src="/images/logo.png" alt="Logo">
      <p>欢迎使用与安服务管理系统</p>
    </div>
    <!-- #endif -->

    <div v-if="$isFeatureEnabled('enableAdvancedReports')" class="advanced-features">
      <h2>高级功能</h2>
      <p>此功能仅在 BOE 版本中可用</p>
    </div>
  </div>
</template>

<script>
import { conditionalMixin } from '@/conditional-compile'

export default {
  name: 'ExamplePage',
  mixins: [conditionalMixin],
  computed: {
    pageTitle() {
      return this.$byCompany({
        boe: 'BOE 管理系统',
        default: '与安管理系统'
      }, '管理系统')
    }
  }
}
</script>

<style scoped>
/* #ifdef COMPANY_BOE */
.boe-banner {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  padding: 20px;
  border-radius: 8px;
}
/* #endif */

/* #ifdef COMPANY_DEFAULT */
.default-banner {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 20px;
  border-radius: 8px;
}
/* #endif */
</style>
```

### 2. 构建和测试

```bash
# 构建 BOE 版本
yarn build:boe

# 构建默认版本
yarn build

# 开发调试 BOE 版本
yarn start:boe
```

### 3. 验证结果

构建完成后，检查：
- `dist-boe/` 目录包含 BOE 版本的构建文件
- `dist/` 目录包含默认版本的构建文件
- 两个版本的代码中只包含对应的条件编译内容

## 总结

条件编译功能为项目的私有化部署提供了强大的支持，通过合理使用编译时和运行时条件编译，可以在保持代码整洁的同时满足不同公司的定制需求。遵循最佳实践和命名规范，可以确保项目的可维护性和扩展性。
