<template>
    <div id="app">
        <RouterView />
    </div>
</template>

<script>
window.addEventListener('beforeunload',e=>{
    window.scroll(0,0)
})
export default {
    provide() {
        return {
            generateI18nTitle: this.generateI18nTitle
        }
    },
    data() {
        return {
            path: '',
            socket: undefined,
            confirmMsg: undefined
        }
    },
    watch: {
        $route: {
            handler: 'routeChange',
            immediate: true
        },
        '$store.state.keepAlive.list'(val) {
            process.env.NODE_ENV == 'development' && console.log(`[ keepAliveList ] ${val}`)
        },
        '$store.state.settings.mode': {
            handler() {
                if (this.$store.state.settings.mode == 'pc') {
                    this.$store.commit('settings/updateThemeSetting', {
                        'sidebarCollapse': this.$store.state.settings.sidebarCollapseLastStatus
                    })
                } else if (this.$store.state.settings.mode == 'mobile') {
                    this.$store.commit('settings/updateThemeSetting', {
                        'sidebarCollapse': true
                    })
                }
                document.body.setAttribute('data-mode', this.$store.state.settings.mode)
            },
            immediate: true
        },
        '$store.state.settings.layout': {
            handler() {
                document.body.setAttribute('data-layout', this.$store.state.settings.layout)
            },
            immediate: true
        },
        '$store.state.settings.theme': {
            handler() {
                document.body.setAttribute('data-theme', this.$store.state.settings.theme)
            },
            immediate: true
        },
        '$store.state.settings.showHeader': {
            handler() {
                document.body.removeAttribute('data-no-main-sidebar')
                if (this.$store.state.settings.showHeader || (this.$store.state.menu.routes.length <= 1 && !this.$store.state.settings.alwaysShowMainSidebar)) {
                    document.body.setAttribute('data-no-main-sidebar', '')
                }
            },
            immediate: true
        },
        '$store.state.menu.routes': {
            handler() {
                document.body.removeAttribute('data-no-main-sidebar')
                if (this.$store.state.settings.showHeader || (this.$store.state.menu.routes.length <= 1 && !this.$store.state.settings.alwaysShowMainSidebar)) {
                    document.body.setAttribute('data-no-main-sidebar', '')
                }
            },
            immediate: true,
            deep: true
        },
        '$store.state.settings.sidebarCollapse': {
            handler() {
                document.body.removeAttribute('data-sidebar-no-collapse')
                document.body.removeAttribute('data-sidebar-collapse')
                if (this.$store.state.settings.sidebarCollapse) {
                    document.body.setAttribute('data-sidebar-collapse', '')
                } else {
                    document.body.setAttribute('data-sidebar-no-collapse', '')
                }
            },
            immediate: true
        }
    },
    beforeDestroy() {
        if (this.socket) {
            this.socket.close()
        }
    },
    mounted() {
        this.path = `${process.env.VUE_APP_WS_API_ROOT}/bms/order/ws?token=${this.$store.state.user.token}`
        window.onresize = () => {
            this.$store.commit('settings/setMode', document.body.clientWidth)
        }
        window.onresize()
        // this.fetchNotHandleCount()
        const currentURL = this.$route.path
        console.log("currentURL:"+window.location.href );
        console.log("path:"+this.$route.path );
        if (currentURL === '/mp' || currentURL === '/' || currentURL === '/login') {
            return;
        }
        this.init()
    },
    methods: {
        // 路由 title 转国际化，如果没有配置则默认显示 title -
        generateI18nTitle(key, defaultTitle) {
            let title
            if (this.$te(key)) {
                title = this.$t(key)
            } else {
                title = defaultTitle
            }
            return title
        },
        // 监听路由变化，更新页面 title
        routeChange() {
            this.$route.meta.title && this.$store.commit('settings/setTitle', this.generateI18nTitle(this.$route.meta.i18n, this.$route.meta.title))
            const currentURL = this.$route.path
            console.log("currentURL:"+window.location.href );
            console.log("path:"+this.$route.path );
            if (currentURL === '/mp' || currentURL === '/' || currentURL === '/login') {
                return;
            }
            // 用于登录后首次内容提醒
            // if (this.$route.query.needTip) {
                this.fetchNotHandleCount()
            // }
        },
        fetchNotHandleCount() {
            console.log("token:"+this.$store.state.user.token)
            if (this.$store.state.user.token) {
                this.$api.get('/bms/event/not-handle/getCount', { }).then(res => {
                    if (res.status === '00000') {
                        this.$store.commit('menuBadge/setUnprocessedNumber', res.data || 0)
                        // this.$notify({
                        //     title: '未处理事件提醒',
                        //     message: `你当前共有${res.data || 0}条未处理事件`,
                        //     type: 'warning',
                        //     position: 'bottom-right'
                        // });
                        if (this.$route.query.needTip && res.data > 0) {
                            let _newQuery = JSON.parse(JSON.stringify(this.$route.query))
                            delete _newQuery.needTip
                            this.$router.replace({ query: _newQuery })
                            this.$confirm(`您有${res.data || 0}个未处理告警事件?`, '', {
                                confirmButtonText: '我知道了',
                                showCancelButton: false,
                                type: 'error',
                                // center: true
                            }).then(() => {
                            });
                        }
                    } else {
                    }
                })
            }
        },
        init() {
            if (WebSocket === undefined) {
                alert('您的浏览器不支持socket')
            } else {
                // 实例化socket
                this.socket = new WebSocket(this.path)
                // 监听socket连接
                this.socket.onopen = this.open
                // 监听socket错误信息
                this.socket.onerror = this.error
                // 监听socket消息
                this.socket.onmessage = this.getMessage
                // 监听socket消息
                this.socket.onclose  = this.close
            }
        },
        open() {
            console.log('socket连接成功')
        },
        error(e) {
            console.log('连接错误', e)
            // this.init()
        },
        getMessage(msg) {
            console.log('msg.data', msg)
            let _val = msg.data;
            if (_val) {
                this.$api.get('/bms/event/not-handle/getCount', { }).then(res => {
                    if (res.status === '00000') {
                        this.$store.commit('menuBadge/setUnprocessedNumber', res.data || 0)
                    }
                });
                if (_val.indexOf('rt_event_add_happen@') != -1) {
                    this.$eventBus.$emit('ServicePlatformUnProcessed-Refresh')
                    let _arrs = _val.split('@');
                    if (this.confirmMsg) {
                        this.$msgbox.close()
                        this.confirmMsg = undefined
                    }
                    if (this.$route.name === 'servicePlatformEventDetail') {
                        this.confirmMsg = this.$confirm('您有一条新的未处理告警事件?', '事件提醒', {
                            confirmButtonText: '立即处理',
                            cancelButtonText: '一会儿处理',
                            type: 'error'
                        }).then(() => {
                            this.$router.push({ name: 'servicePlatformEventDetail', params: { id: _arrs[1] } })
                        }).catch(() => {
                        });
                    }
                } else if (_val.indexOf('rt_event_update_happen@') != -1) {
                    this.$eventBus.$emit('ServicePlatformUnProcessed-Refresh')
                    let _arrs = _val.split('@');
                    let _isCurrent = this.$route.name === 'servicePlatformEventDetail' && this.$route.params.id && (this.$route.params.id + '') === (_arrs[1] + '');
                    console.log(_val, _val.split('@'), _isCurrent, this.$route.name, this.$route.params.id, _arrs[1], (this.$route.params.id + '') === (_arrs[1] + ''))
                    this.$message({
                        type: 'warning',
                        message: _isCurrent ? '当前事件有新的告警更新' : '有新的告警',
                        duration: 2500
                    })
                    if (_isCurrent) {
                        this.$eventBus.$emit('ServicePlatformEventDetail-HisRefresh')
                    }
                } else if (_val.indexOf('rt_event_handle_happen@') != -1) {
                    console.log('处理事件, 刷新状态')
                }
            }
        },
        close() {
            console.log('socket已经关闭')
        }
    },
    metaInfo() {
        return {
            title: this.$store.state.settings.enableDynamicTitle && this.$store.state.settings.title,
            titleTemplate: title => {
                let t =  title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
                if(this.$store.state.user.member.systemVersion == '2'){
                    return t?t+" - 高级版":process.env.VUE_APP_TITLE+" - 高级版"
                }
            }
        }
    }
}
</script>

<style scoped>
#app {
    height: 100%;
}
</style>
<style lang="scss">
.el-select-dropdown {
    background: rgba(1, 12, 57, 0.78) !important;
    border-color: rgb(64, 158, 255) !important;
    &__item {
        color: white !important;
        &:hover {
            background-color: #2e4e7d !important;
        }
        &.selected {
            background-color: #2e4e7d !important;
        }
    }
    &__item.hover {
        background-color: #2e4e7d !important;
    }
}
.el-popper[x-placement^=bottom] .popper__arrow {
    top: -7px !important;
    border-bottom-color: #409eff !important;
    &::after {
        border-bottom-color: rgb(64 158 255) !important;
    }
}
.el-table__header {
    th {
        background: #fafafa;
    }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
}
input[type="number"] { -moz-appearance: textfield; }
// .el-select-dropdown {
//     border: 1px solid #e1ece1 !important;
//     .el-select-dropdown__item {
//         color: #333 !important;
//     }
// }
// 解决element-ui的表格设置固定栏后，边框线消失的bug
::v-deep .el-table__row {
    td:not(.is-hidden):last-child {
        right: -1px;
    }
}
::v-deep .el-table.theme table thead tr th {
    background: #39f !important;
    border-right: 1px solid #fff;
    border-bottom: none;
    right: 1px;
}
</style>
