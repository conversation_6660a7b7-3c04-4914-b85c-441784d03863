# ECare应用产品需求文档

## 目录
- [1. 概述](#1-概述)
- [2. 功能模块划分](#2-功能模块划分)
- [3. 详细功能描述](#3-详细功能描述)
- [4. 技术架构思想](#4-技术架构思想)
- [5. 界面设计规范](#5-界面设计规范)
- [6. 交互流程设计](#6-交互流程设计)

## 1. 概述

### 1.1 应用定位
ECare是一款智能看护系统的移动客户端应用，专为家庭看护场景设计。应用通过集成VoIP通话、设备配网、WebView桥接等核心功能，为用户提供完整的智能看护解决方案。

### 1.2 目标用户
- **主要用户**：需要远程看护家庭成员的用户
- **使用场景**：家庭看护、远程监控、设备管理
- **用户特征**：对智能设备有基本了解，重视家庭安全和便捷操作

### 1.3 核心价值
- **实时通话**：提供高质量的VoIP语音通话功能
- **设备管理**：简化智能设备的配网和管理流程
- **统一体验**：通过WebView桥接实现原生功能与H5页面的无缝集成
- **安全可靠**：完善的用户认证和数据安全保障

## 2. 功能模块划分

### 2.1 用户认证模块
负责用户身份验证、登录状态管理和用户信息存储。

### 2.2 VoIP通话模块
提供高质量的语音通话功能，支持呼入呼出、通话控制等。

### 2.3 WebView桥接模块
实现原生功能与H5页面的双向通信，提供统一的用户体验。

### 2.4 ESP设备配网模块
通过蓝牙连接ESP设备，完成WiFi网络配置和设备入网。

### 2.5 扫描功能模块
提供二维码和条形码扫描功能，支持多种扫码场景。

### 2.6 应用管理模块
包括启动管理、权限管理、性能监控等基础功能。

## 3. 详细功能描述

### 3.1 用户认证模块

#### 3.1.1 登录功能
**功能描述**：支持密码登录和验证码登录两种方式

**界面设计**：
- 顶部显示应用Logo和欢迎文字
- 中央区域为登录表单，包含手机号输入框
- 根据登录模式显示密码输入框或验证码输入框
- 底部包含登录按钮、模式切换链接、注册和忘记密码链接

**交互流程**：
1. 用户选择登录模式（密码/验证码）
2. 输入手机号码（必填，格式验证）
3. 密码模式：输入密码；验证码模式：获取并输入验证码
4. 点击登录按钮，显示加载状态
5. 登录成功后跳转到主界面，失败则显示错误提示

**业务逻辑**：
- 手机号格式验证（11位数字，1开头）
- 密码长度验证（6-20位字符）
- 验证码倒计时机制（60秒）
- 登录状态持久化存储
- Token自动刷新机制

#### 3.1.2 自动登录
**功能描述**：应用启动时自动检查登录状态，已登录用户直接进入主界面

**业务逻辑**：
- 检查本地存储的Token有效性
- Token有效：直接跳转主界面
- Token无效或不存在：跳转登录界面
- 支持开机自启动场景的特殊处理

### 3.2 VoIP通话模块

#### 3.2.1 通话发起
**功能描述**：支持从H5页面发起VoIP语音通话

**界面设计**：
- 外呼界面：显示被叫号码、联系人头像、通话状态
- 操作按钮：挂断按钮（红色圆形，居中显示）
- 状态显示：呼叫中、连接中、通话中等状态文字

**交互流程**：
1. H5页面通过桥接调用原生通话功能
2. 显示外呼界面，播放拨号音
3. 等待对方接听或超时
4. 接听成功后切换到通话界面

#### 3.2.2 来电接听
**功能描述**：接收并处理来电，支持锁屏状态下的来电显示

**界面设计**：
- 全屏来电界面，支持锁屏显示
- 显示来电号码、联系人头像
- 底部操作按钮：接听（绿色）、挂断（红色）

**交互流程**：
1. 接收到来电时显示来电界面
2. 用户选择接听或挂断
3. 接听后切换到通话界面
4. 挂断后关闭来电界面

#### 3.2.3 通话控制
**功能描述**：通话过程中的音频控制功能

**界面设计**：
- 通话界面显示对方信息和通话时长
- 底部控制按钮：静音、扬声器、挂断
- 按钮状态实时更新（激活/非激活状态）

**功能列表**：
- 静音/取消静音
- 扬声器/听筒切换
- 挂断通话
- 通话时长计时

### 3.3 WebView桥接模块

#### 3.3.1 页面加载
**功能描述**：加载H5页面并提供原生功能桥接

**界面设计**：
- 全屏WebView显示H5内容
- 顶部进度条显示页面加载进度
- 支持系统状态栏适配

**技术实现**：
- 自定义WebViewClient处理页面导航
- 自定义WebChromeClient处理页面标题和进度
- JavaScript桥接对象注入

#### 3.3.2 原生功能调用
**功能描述**：H5页面调用原生功能的统一接口

**支持功能**：
- VoIP通话发起
- 设备信息获取
- 扫码功能调用
- 本地数据存储
- 网络状态获取
- 页面导航控制

**消息格式**：
- 统一的JSON消息格式
- 支持Promise和事件通知两种模式
- 错误处理和状态反馈机制

### 3.4 ESP设备配网模块

#### 3.4.1 设备扫描
**功能描述**：通过蓝牙扫描发现ESP设备

**界面设计**：
- 设备列表界面，显示扫描到的设备
- 每个设备项显示：设备名称、MAC地址、信号强度、设备类型标识
- 顶部显示扫描状态和设备数量
- 底部操作按钮：开始扫描、停止扫描

**交互流程**：
1. 检查蓝牙和位置权限
2. 开始蓝牙设备扫描
3. 实时更新设备列表
4. 用户选择目标设备进行连接

#### 3.4.2 设备连接
**功能描述**：连接到选中的ESP设备

**界面设计**：
- 连接进度界面，显示连接状态
- 设备信息展示：名称、MAC地址、信号强度
- 连接状态指示器和文字说明

**交互流程**：
1. 用户选择设备后开始连接
2. 显示连接进度和状态
3. 连接成功后进入WiFi配置流程
4. 连接失败显示错误信息和重试选项

#### 3.4.3 WiFi配置
**功能描述**：为ESP设备配置WiFi网络连接

**界面设计**：
- WiFi网络列表，显示扫描到的网络
- 每个网络项显示：SSID、信号强度、加密类型
- 密码输入界面，支持密码显示/隐藏切换
- 配网进度界面，显示配置状态

**交互流程**：
1. 扫描可用WiFi网络
2. 用户选择目标网络
3. 输入WiFi密码
4. 开始配网流程
5. 显示配网结果

### 3.5 扫描功能模块

#### 3.5.1 扫码界面
**功能描述**：提供二维码和条形码扫描功能

**界面设计**：
- 全屏相机预览
- 中央扫描框，带有动画效果
- 顶部标题栏显示扫码类型
- 底部操作按钮：闪光灯开关、相册选择

**交互流程**：
1. 检查相机权限
2. 启动相机预览
3. 实时检测扫描框内的码
4. 识别成功后返回结果
5. 支持连续扫描模式

#### 3.5.2 扫码类型支持
**支持格式**：
- 二维码：QR Code
- 条形码：Code 128、Code 39、Code 93、EAN-13、EAN-8、UPC-A、UPC-E
- 全格式：支持所有常见格式

## 4. 技术架构思想

### 4.1 MVVM架构模式
**设计理念**：采用MVVM（Model-View-ViewModel）架构模式，实现界面与业务逻辑的分离

**组件职责**：
- **Model**：数据模型和业务逻辑，包括网络请求、数据存储
- **View**：用户界面，负责数据展示和用户交互
- **ViewModel**：连接View和Model，处理界面逻辑和数据绑定

**实现方式**：
- 使用LiveData实现数据观察和自动更新
- 通过DataBinding实现双向数据绑定
- Repository模式管理数据源

### 4.2 依赖注入设计
**设计目标**：通过依赖注入实现组件解耦和测试友好

**实现策略**：
- 使用Hilt框架进行依赖注入
- 单例模式管理全局组件
- 接口抽象实现可替换性

### 4.3 模块化设计
**模块划分**：
- 按功能领域划分模块
- 每个模块包含完整的MVC结构
- 模块间通过接口通信

### 4.4 数据绑定策略
**绑定类型**：
- **ViewBinding**：用于数据展示为主的页面
- **DataBinding**：用于表单页面和复杂交互

**状态管理**：
- 使用LiveData管理界面状态
- 通过ViewModel持有和管理数据
- 支持配置变更时的状态保持

## 5. 界面设计规范

### 5.1 色彩规范
**主色调**：
- 主色：#01B09A（青绿色）
- 辅助色：#009a90（深青绿色）
- 背景色：#FFFFFF（白色）
- 文字色：#333333（深灰色）

**功能色彩**：
- 成功：#4CAF50（绿色）
- 警告：#FF9800（橙色）
- 错误：#F44336（红色）
- 信息：#2196F3（蓝色）

### 5.2 字体规范
**字体大小**：
- 标题：18sp-24sp
- 正文：14sp-16sp
- 辅助文字：12sp-14sp
- 按钮文字：16sp

**字体权重**：
- 标题：Medium/Bold
- 正文：Regular
- 辅助文字：Regular

### 5.3 间距规范
**页面边距**：16dp-24dp
**组件间距**：8dp-16dp
**内容间距**：4dp-8dp

### 5.4 图标设计
**图标风格**：线性图标，统一风格
**图标尺寸**：24dp（标准）、32dp（大图标）
**图标颜色**：跟随主题色彩

## 6. 交互流程设计

### 6.1 应用启动流程
1. **启动检查**：检查应用权限和网络状态
2. **登录验证**：检查本地登录状态
3. **页面跳转**：根据登录状态跳转到相应页面
4. **服务初始化**：启动VoIP服务和其他后台服务

### 6.2 用户登录流程
1. **模式选择**：用户选择登录方式
2. **信息输入**：输入手机号和密码/验证码
3. **验证提交**：提交登录请求
4. **结果处理**：处理登录结果，成功则跳转主页

### 6.3 VoIP通话流程
1. **通话发起**：H5页面调用通话接口
2. **界面显示**：显示外呼界面
3. **连接建立**：等待对方接听
4. **通话控制**：提供通话控制功能
5. **通话结束**：挂断后清理资源

### 6.4 设备配网流程
1. **权限检查**：检查蓝牙和位置权限
2. **设备扫描**：扫描并显示ESP设备
3. **设备连接**：连接到选中设备
4. **网络扫描**：扫描WiFi网络
5. **密码输入**：输入WiFi密码
6. **配网执行**：执行配网流程
7. **结果反馈**：显示配网结果

### 6.5 扫码功能流程
1. **权限检查**：检查相机权限
2. **相机启动**：启动相机预览
3. **扫码检测**：实时检测扫描框内容
4. **结果处理**：识别成功后处理结果
5. **结果返回**：将结果返回给调用方

## 7. 数据模型设计

### 7.1 用户数据模型
**UserData**：
- userId：用户唯一标识
- phoneNumber：手机号码
- nickname：用户昵称
- avatar：头像URL
- token：访问令牌
- refreshToken：刷新令牌
- loginTime：登录时间
- lastActiveTime：最后活跃时间

### 7.2 设备数据模型
**ESPDevice**：
- macAddress：设备MAC地址（唯一标识）
- name：设备名称
- rssi：信号强度值
- isConnected：连接状态
- deviceType：设备类型
- firmwareVersion：固件版本
- lastSeen：最后发现时间
- serviceUuid：蓝牙服务UUID

### 7.3 WiFi网络模型
**WiFiNetwork**：
- ssid：网络名称
- bssid：网络BSSID
- rssi：信号强度
- security：加密类型
- frequency：频率
- isConnected：是否已连接

### 7.4 通话信息模型
**CallInfo**：
- callId：通话唯一标识
- phoneNumber：电话号码
- displayName：显示名称
- isIncoming：是否为来电
- startTime：通话开始时间
- duration：通话时长
- callState：通话状态

## 8. 状态管理设计

### 8.1 应用状态
**AppState**：
- isInitialized：应用是否已初始化
- isLoggedIn：用户是否已登录
- networkStatus：网络连接状态
- permissionStatus：权限状态集合

### 8.2 VoIP状态
**VoIPState**：
- isRegistered：SIP注册状态
- currentCall：当前通话信息
- callState：通话状态（空闲、呼出、呼入、通话中等）
- audioState：音频状态（静音、扬声器等）

### 8.3 配网状态
**ProvisioningState**：
- currentStep：当前配网步骤
- isScanning：是否正在扫描
- discoveredDevices：发现的设备列表
- connectedDevice：已连接的设备
- wifiNetworks：WiFi网络列表
- provisioningResult：配网结果

## 9. 错误处理机制

### 9.1 网络错误处理
**错误类型**：
- 连接超时：显示网络连接超时提示
- 服务器错误：显示服务器异常提示
- 认证失败：跳转到登录页面
- 数据解析错误：显示数据格式错误提示

**处理策略**：
- 自动重试机制（最多3次）
- 用户手动重试选项
- 降级处理方案
- 错误日志记录

### 9.2 权限错误处理
**权限类型**：
- 相机权限：扫码功能必需
- 蓝牙权限：设备配网必需
- 位置权限：蓝牙扫描必需
- 麦克风权限：VoIP通话必需

**处理流程**：
1. 检查权限状态
2. 请求缺失权限
3. 处理用户响应
4. 提供权限说明和引导

### 9.3 业务错误处理
**登录错误**：
- 手机号格式错误
- 密码错误
- 验证码错误或过期
- 账户被锁定

**通话错误**：
- SIP注册失败
- 通话连接失败
- 音频设备异常
- 网络质量差

**配网错误**：
- 设备连接失败
- WiFi密码错误
- 配网超时
- 设备不支持

## 10. 性能优化要求

### 10.1 启动性能
**优化目标**：
- 冷启动时间 < 3秒
- 热启动时间 < 1秒
- 首屏渲染时间 < 2秒

**优化策略**：
- 关键路径优先加载
- 非关键功能延迟初始化
- 资源预加载和缓存
- 启动页面优化

### 10.2 内存管理
**管理策略**：
- 及时释放不用的资源
- 图片压缩和缓存优化
- 避免内存泄漏
- 合理使用对象池

### 10.3 网络优化
**优化措施**：
- 请求合并和缓存
- 数据压缩传输
- 连接池复用
- 超时和重试机制

### 10.4 电池优化
**节能策略**：
- 后台任务优化
- 定位服务合理使用
- 网络请求频率控制
- 屏幕常亮时间控制

## 11. 安全性要求

### 11.1 数据安全
**加密存储**：
- 用户敏感信息加密存储
- Token安全存储
- 本地数据库加密

**传输安全**：
- HTTPS协议传输
- 数据传输加密
- 证书验证

### 11.2 身份认证
**认证机制**：
- Token有效期管理
- 自动刷新机制
- 多设备登录控制
- 异常登录检测

### 11.3 权限控制
**最小权限原则**：
- 按需申请权限
- 权限使用说明
- 权限状态检查
- 权限异常处理

## 12. 兼容性要求

### 12.1 系统版本兼容
**iOS版本支持**：
- 最低支持：iOS 12.0
- 推荐版本：iOS 14.0+
- 最新版本：iOS 17.0+

### 12.2 设备兼容
**设备支持**：
- iPhone 6s及以上机型
- iPad（第6代）及以上
- iPod touch（第7代）及以上

### 12.3 屏幕适配
**屏幕尺寸**：
- 4.7英寸（iPhone SE）
- 5.4英寸（iPhone 12 mini）
- 6.1英寸（iPhone 12/13/14）
- 6.7英寸（iPhone 12/13/14 Pro Max）

**适配策略**：
- 响应式布局设计
- 安全区域适配
- 动态字体支持
- 横竖屏适配

## 13. 测试要求

### 13.1 功能测试
**测试范围**：
- 用户登录注册流程
- VoIP通话功能
- 设备配网流程
- 扫码功能
- WebView桥接功能

### 13.2 性能测试
**测试指标**：
- 启动时间测试
- 内存使用测试
- CPU使用率测试
- 网络性能测试
- 电池消耗测试

### 13.3 兼容性测试
**测试维度**：
- 不同iOS版本测试
- 不同设备型号测试
- 不同网络环境测试
- 不同屏幕尺寸测试

### 13.4 安全测试
**测试内容**：
- 数据传输安全测试
- 本地存储安全测试
- 权限控制测试
- 异常情况处理测试

## 14. 发布和维护

### 14.1 版本管理
**版本号规则**：
- 主版本号：重大功能更新
- 次版本号：功能增加和改进
- 修订版本号：Bug修复

### 14.2 发布流程
**发布步骤**：
1. 代码审查和测试
2. 版本打包和签名
3. App Store审核提交
4. 发布后监控和反馈

### 14.3 维护计划
**维护内容**：
- Bug修复和性能优化
- 新功能开发和迭代
- 系统版本适配更新
- 安全漏洞修复

---

*本文档基于ECare Android版本分析生成，用于指导iOS版本开发。文档版本：v1.0，更新日期：2024年*
