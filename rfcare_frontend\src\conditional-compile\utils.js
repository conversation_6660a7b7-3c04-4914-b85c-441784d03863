/**
 * 条件编译工具函数
 * 提供运行时的条件编译支持
 * 
 * <AUTHOR>
 */

import { getCurrentCompanyConfig, isCompany, SUPPORTED_COMPANIES } from './config'

/**
 * 根据公司条件渲染组件
 * @param {string} company - 公司代码
 * @param {Function} component - 要渲染的组件
 * @returns {Function|null} 组件或null
 */
export const renderByCompany = (company, component) => {
  return isCompany(company) ? component : null
}

/**
 * 根据公司条件执行函数
 * @param {string} company - 公司代码
 * @param {Function} fn - 要执行的函数
 * @param {...any} args - 函数参数
 * @returns {any} 函数执行结果或undefined
 */
export const executeByCompany = (company, fn, ...args) => {
  return isCompany(company) ? fn(...args) : undefined
}

/**
 * 根据公司条件返回不同的样式类名
 * @param {Object} classMap - 公司代码到样式类名的映射
 * @param {string} defaultClass - 默认样式类名
 * @returns {string} 样式类名
 */
export const getClassByCompany = (classMap, defaultClass = '') => {
  const config = getCurrentCompanyConfig()
  return classMap[config.code] || defaultClass
}

/**
 * 根据公司条件返回不同的文本内容
 * @param {Object} textMap - 公司代码到文本内容的映射
 * @param {string} defaultText - 默认文本内容
 * @returns {string} 文本内容
 */
export const getTextByCompany = (textMap, defaultText = '') => {
  const config = getCurrentCompanyConfig()
  return textMap[config.code] || defaultText
}

/**
 * 根据公司条件返回不同的配置项
 * @param {Object} configMap - 公司代码到配置的映射
 * @param {any} defaultConfig - 默认配置
 * @returns {any} 配置项
 */
export const getConfigByCompany = (configMap, defaultConfig = null) => {
  const config = getCurrentCompanyConfig()
  return configMap[config.code] || defaultConfig
}

/**
 * 检查当前公司是否支持某个功能
 * @param {string} featureName - 功能名称
 * @returns {boolean} 是否支持
 */
export const isFeatureEnabled = (featureName) => {
  const config = getCurrentCompanyConfig()
  return config.features && config.features[featureName] === true
}

/**
 * 获取当前公司的主题配置
 * @returns {Object} 主题配置
 */
export const getThemeConfig = () => {
  const config = getCurrentCompanyConfig()
  return config.theme || {}
}

/**
 * 获取当前公司的API配置
 * @returns {Object} API配置
 */
export const getApiConfig = () => {
  const config = getCurrentCompanyConfig()
  return config.api || {}
}

/**
 * Vue 混入对象，提供条件编译相关的方法
 */
export const conditionalMixin = {
  data() {
    return {
      currentCompany: getCurrentCompanyConfig()
    }
  },
  methods: {
    // 检查是否为指定公司
    $isCompany(company) {
      return isCompany(company)
    },
    
    // 检查是否为 BOE 公司
    $isBOE() {
      return isCompany(SUPPORTED_COMPANIES.BOE)
    },
    
    // 检查是否为默认配置
    $isDefault() {
      return isCompany(SUPPORTED_COMPANIES.DEFAULT)
    },
    
    // 根据公司条件返回不同的值
    $byCompany(configMap, defaultValue = null) {
      return getConfigByCompany(configMap, defaultValue)
    },
    
    // 检查功能是否启用
    $isFeatureEnabled(featureName) {
      return isFeatureEnabled(featureName)
    },
    
    // 获取主题配置
    $getThemeConfig() {
      return getThemeConfig()
    },
    
    // 获取API配置
    $getApiConfig() {
      return getApiConfig()
    }
  }
}

export default {
  renderByCompany,
  executeByCompany,
  getClassByCompany,
  getTextByCompany,
  getConfigByCompany,
  isFeatureEnabled,
  getThemeConfig,
  getApiConfig,
  conditionalMixin
}
